# Mock Data CSV Files

This directory contains CSV files with mock data for the TPA API endpoints. The data is designed to support all three API endpoints with realistic insurance policy and claims information.

## File Structure

### 1. `policies.csv`
Contains policy and member information for the **PolicyListSF** endpoint.
- **Records**: 12 members (11 active, 1 inactive)
- **Key Fields**: MemberCode, PolicyNo, CitizenID, Names (TH/EN), InsuranceDetails, etc.
- **Test Scenarios**: 
  - Active and inactive policies
  - VIP and standard members
  - Different insurance companies and plans
  - Various member types (Principal, Dependent)

### 2. `payment_details.csv`
Contains payment method information linked to members.
- **Records**: 17 payment methods across members
- **Key Fields**: MemberCode, PaymentMethod, PayeeName, BankAccNo, BankName, Primary
- **Test Scenarios**:
  - Multiple payment methods per member
  - Different payment types (Bank Transfer, Credit Card, Cash)
  - Primary and secondary payment methods

### 3. `policy_details.csv`
Contains main benefit information for the **PolicyDetailSF** endpoint.
- **Records**: 12 main benefits (one per member)
- **Key Fields**: MemberCode, MainBenefitCode, MainBenefit, Limits, Balances
- **Test Scenarios**:
  - Different benefit types and amounts
  - Various limit structures
  - Balance tracking

### 4. `benefits.csv`
Contains detailed benefit breakdown for each member.
- **Records**: 12 benefit records with sub-benefits
- **Key Fields**: BenefitCode, SubBenefitCode, Limits, Balances, Combined limits
- **Test Scenarios**:
  - Multiple benefit levels (Benefit, Sub-benefit, Combined)
  - Complex limit structures
  - Balance calculations

### 5. `contract_conditions.csv`
Contains contract-level conditions and restrictions.
- **Records**: 11 contract conditions
- **Key Fields**: ConditionType, EffectiveDates, ConditionDetail, Action
- **Test Scenarios**:
  - Different condition types (Exclusion, Waiting Period, VIP Service)
  - Active and inactive conditions
  - Audit trail (CreateBy, ModifiedBy)

### 6. `member_conditions.csv`
Contains member-specific conditions and medical history.
- **Records**: 11 member conditions
- **Key Fields**: ConditionType, EffectiveDates, ConditionDetail, Action
- **Test Scenarios**:
  - Medical conditions and monitoring
  - Special member statuses
  - Health alerts and restrictions

### 7. `claims.csv`
Contains claims history for the **ClaimListSF** endpoint.
- **Records**: 13 claims across different members
- **Key Fields**: ClaimNo, ClaimStatus, Amounts, Dates, Diagnosis, Provider
- **Test Scenarios**:
  - All required claim statuses: Approved, Authoried, Open, Paid, Pending, Pending For Approval, Rejected
  - Different claim types: Inpatient, Outpatient, Emergency, Accident
  - Various medical conditions and treatments
  - Different hospitals and providers

## Data Relationships

- **MemberCode** is the primary key linking all data
- **PolicyNo** groups members under the same policy
- **CitizenID** provides unique identification
- **InsurerCode** and **CompanyCode** provide organizational grouping

## Test Coverage

The mock data covers all API parameter combinations specified in the requirements:

### PolicyListSF Parameters:
- ✅ INSURER_CODE + CITIZEN_ID
- ✅ INSURER_CODE + POLICY_NO + NAME_TH
- ✅ INSURER_CODE + POLICY_NO + NAME_EN
- ✅ INSURER_CODE + CERTIFICATE_NO + NAME_TH
- ✅ INSURER_CODE + CERTIFICATE_NO + NAME_EN
- ✅ INSURER_CODE + STAFF_NO + NAME_TH
- ✅ INSURER_CODE + STAFF_NO + NAME_EN
- ✅ INSURER_CODE + OTHER_ID
- ✅ INSURER_CODE + NAME_TH
- ✅ INSURER_CODE + NAME_EN

### ClaimListSF Parameters:
- ✅ MEMBER_CODE
- ✅ INSURER_CODE + CITIZEN_ID

### Claim Statuses:
- ✅ Approved
- ✅ Authoried
- ✅ Open
- ✅ Paid
- ✅ Pending
- ✅ Pending For Approval
- ✅ Rejected

## Usage Notes

1. **Date Formats**: All dates are in YYYY-MM-DD format
2. **Active Policies**: Only policies with PlanEffFrom <= today <= PlanEffTo should be returned
3. **Thai/English**: Data includes both Thai and English names/descriptions
4. **Amounts**: All monetary amounts are in Thai Baht
5. **Member Codes**: Follow pattern MEM001, MEM002, etc.
6. **Policy Numbers**: Follow pattern POL001, POL002, etc.

This mock data will be loaded into in-memory data structures when the API starts up, providing realistic test scenarios for all endpoints.
