# TPA API Requirements

- Create 3 REST API endpoints that return information about insurance policies and claims as follow:
    1. PolicyListSF
    2. PolicyDetailSF
    3. ClaimListSF
- All data are mocked.
- Use path-based routing e.g.,
    - ${DOMAIN}/api/PolicyListSF
    - ${DOMAIN}/api/PolicyDetailSF
    - ${DOMAIN}/api/ClaimListSF

## PolicyListSF

API Parameters:
- INSURER_CODE
- CITIZEN_ID
- POLICY_NO
- CERTIFICATE_NO
- STAFF_NO
- OTHER_ID
- NAME_TH
- NAME_EN

API Return:
[List] ListOfPolicy
    [String] MemberCode
    [String] MemberStatus
    [String] TitleTH
    [String] NameTH
    [String] SurnameTH
    [String] TitleEN
    [String] NameEN
    [String] SurnameEN
    [String] CitizenID
    [String] OtherID
    [String] StaffNo
    [String] InsurerCardNo
    [String] InsPreviousCardNo
    [String] PolicyNo
    [String] CertificateNo
    [String] MemberType
    [String] PrincipleMemberCode
    [String] PrincipleName
    [String] VIP
    [String] VIPRemarks
    [String] CardType
    [String] Language
    [String] InsurerCode
    [String] InsurerName
    [String] InsurerNameEN
    [String] CompanyCode
    [String] CompanyName
    [String] CompanyNameEN
    [String] BirthDate
    [String] Gender
    [String] Citizenship
    [String] CountryCode
    [String] PlanCode
    [String] PlanName
    [String] PlanEffFrom
    [String] PlanEffTo
    [String] Mobile
    [String] Email
    [List] PaymentDetail
        [String] PaymentMethod
        [String] PayeeName
        [String] BankAccNo
        [String] BankName
        [String] Primary

API Conditions:
- API accepts the following pairs of parameters:
    - INSURER_CODE, CITIZEN_ID
    - INSURER_CODE, POLICY_NO, NAME_TH
    - INSURER_CODE, POLICY_NO, NAME_EN
    - INSURER_CODE, CERTIFICATE_NO, NAME_TH
    - INSURER_CODE, CERTIFICATE_NO, NAME_EN
    - INSURER_CODE, STAFF_NO, NAME_TH
    - INSURER_CODE, STAFF_NO, NAME_EN
    - INSURER_CODE, OTHER_ID
    - INSURER_CODE, NAME_TH
    - INSURER_CODE, NAME_EN
- API only returns active policies (PlanEffFrom <= today <= PlanEffTo)


## PolicyDetailSF

API Parameters:
- MEMBER_CODE

API Return:
[List] ListPolicyDetail
    [String] MainBenefitCode
    [String] MainBenefit
    [String] MainBenefitEN
    [String] MainPlanLimitDesc
    [String] MainPlanAmount
    [String] MainPlanUnit1
    [String] MainPlanUnit2
    [String] MainPlanBalance
[List] BenefitList
    [String] BenefitCode
    [String] BenefitTH
    [String] BenefitEN
    [String] SubBenefitCode
    [String] SubBenefitTH
    [String] SubBenefitEN
    [String] LimitCode
    [String] LimitAmt
    [String] LimitUnit
    [String] BalanceCode
    [String] BalanceLimitAmt
    [String] BalanceUnit
    [String] ComSubLimitCode
    [String] ComSubLimitAmt
    [String] ComSubLimitUnit
    [String] BalComSubLimitCode
    [String] BalComSubLimitAmt
    [String] BalComSubLimitUnit
    [String] ComLimitCode
    [String] ComLimitAmt
    [String] ComLimitUnit
    [String] BalComLimitCode
    [String] BalComLimitAmt
    [String] BalComLimitUnit
[List] ListContractCondition
    [String] ConditionType
    [String] ConditionApply
    [String] EffFromDate
    [String] EffToDate
    [String] ConditionDetail
    [String] Action
    [String] SourceCondition
    [String] Remarks
    [String] CreateBy
    [String] CreateDateTime
    [String] ModifiedBy
    [String] ModifiedDateTime
[List] ListMemberCondition
    [String] ConditionType
    [String] EffFromDate
    [String] EffToDate
    [String] ConditionDetail
    [String] Action
    [String] SourceCondition
    [String] Remarks
    [String] CreateBy
    [String] CreateDateTime
    [String] ModifiedBy
    [String] ModifiedDateTime
[List] ListClaimHistory
    [String] ClaimNo
    [String] MainBenefit
    [String] ClaimSource
    [String] ClaimType
    [String] PatientNameTH
    [String] PatientNameEN
    [String] ClaimStatus
    [String] CitizenID
    [String] VisitDate
    [String] DischargeDate
    [String] AccidentDate
    [String] IncurredAmt
    [String] PayableAmt
    [String] ContractNo
    [String] InsurerCode
    [String] InsurerTH
    [String] InsurerEN
    [String] CompanyCode
    [String] CompanyTH
    [String] CompanyEN
    [String] CardType
    [String] PolicyNo
    [String] ProviderCode
    [String] ProviderTH
    [String] ProviderEN
    [String] BatchNo
    [String] DiagCode
    [String] DiagTH
    [String] DiagEN
    [String] PaymentDate
    [String] MemberCode


## ClaimListSF

API Parameters:
- MEMBER_CODE
- INSURER_CODE
- CITIZEN_ID

API Returns:
[List] ListClaimHistory
    [String] ClaimNo
    [String] MainBenefit
    [String] ClaimSource
    [String] ClaimType
    [String] PatientNameTH
    [String] PatientNameEN
    [String] ClaimStatus
    [String] CitizenID
    [String] VisitDate
    [String] DischargeDate
    [String] AccidentDate
    [String] IncurredAmt
    [String] PayableAmt
    [String] ContractNo
    [String] InsurerCode
    [String] InsurerTH
    [String] InsurerEN
    [String] CompanyCode
    [String] CompanyTH
    [String] CompanyEN
    [String] CardType
    [String] PolicyNo
    [String] ProviderCode
    [String] ProviderTH
    [String] ProviderEN
    [String] BatchNo
    [String] DiagCode
    [String] DiagTH
    [String] DiagEN
    [String] PaymentDate
    [String] MemberCode

API Conditions:
- API accepts the following pairs of parameters:
    - MEMBER_CODE
    - INSURER_CODE, CITIZEN_ID
- API only returns claims with the following status:
    - Approved
    - Authoried
    - Open
    - Paid
    - Pending
    - Pending For Approval
    - Rejected