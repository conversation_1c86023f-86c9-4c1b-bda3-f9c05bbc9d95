#!/usr/bin/env python3
"""
Test script for Phase 5 API Layer implementation.
Validates that all API components are working correctly.
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all API components can be imported."""
    print("🧪 Testing API Layer Imports...")
    
    try:
        # Test core dependencies
        from src.api.dependencies import (
            get_query_params,
            handle_service_exception,
            get_policy_service_dependency,
            get_claim_service_dependency
        )
        print("✅ API dependencies imported successfully")
        
        # Test route modules
        from src.api.routes.policy_list import router as policy_list_router
        from src.api.routes.policy_detail import router as policy_detail_router
        from src.api.routes.claim_list import router as claim_list_router
        print("✅ API route modules imported successfully")
        
        # Test main API package
        from src.api import policy_list_router, policy_detail_router, claim_list_router
        print("✅ Main API package imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_services():
    """Test that services can be instantiated and work."""
    print("\n🧪 Testing Service Integration...")
    
    try:
        # Initialize data first
        from src.data.mock_data import initialize_data
        initialize_data()
        print("✅ Data initialization successful")
        
        # Test service dependencies
        from src.api.dependencies import get_policy_service_dependency, get_claim_service_dependency
        policy_service = get_policy_service_dependency()
        claim_service = get_claim_service_dependency()
        print("✅ Service dependencies working")
        
        # Test a simple service call
        test_params = {'MEMBER_CODE': 'MEM001'}
        result = policy_service.get_policy_detail(test_params)
        print(f"✅ Policy detail test successful - found {len(result.ListPolicyDetail)} policy details")
        
        # Test claim service
        claim_result = claim_service.get_claim_list(test_params)
        print(f"✅ Claim list test successful - found {len(claim_result)} claims")
        
        return True
        
    except Exception as e:
        print(f"❌ Service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_exception_handling():
    """Test exception handling functionality."""
    print("\n🧪 Testing Exception Handling...")
    
    try:
        from src.api.dependencies import handle_service_exception
        from src.utils.exceptions import ValidationError, NotFoundError
        
        # Test validation error conversion
        test_error = ValidationError("Test validation error")
        http_exception = handle_service_exception(test_error)
        assert http_exception.status_code == 400
        print("✅ ValidationError conversion successful")
        
        # Test not found error conversion
        not_found_error = NotFoundError("Test not found error")
        http_exception = handle_service_exception(not_found_error)
        assert http_exception.status_code == 404
        print("✅ NotFoundError conversion successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Exception handling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all Phase 5 API Layer tests."""
    print("🚀 Phase 5 API Layer Implementation Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_services,
        test_exception_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print(f"❌ Test {test.__name__} failed")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Phase 5 API Layer implementation completed successfully!")
        print("\n📋 Implemented Components:")
        print("  ✅ src/api/dependencies.py - Shared dependencies and validation")
        print("  ✅ src/api/routes/policy_list.py - PolicyListSF endpoint")
        print("  ✅ src/api/routes/policy_detail.py - PolicyDetailSF endpoint")
        print("  ✅ src/api/routes/claim_list.py - ClaimListSF endpoint")
        print("  ✅ Updated response models to match CSV data structure")
        print("  ✅ Fixed service layer integration issues")
        print("\n🎯 Ready for Phase 6: Application Assembly")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
