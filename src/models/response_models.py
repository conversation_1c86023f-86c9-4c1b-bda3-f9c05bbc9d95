"""
Pydantic response models for API endpoints.
Defines response models for PolicyListSF, PolicyDetailSF, and ClaimListSF endpoints.
"""

from typing import List
from pydantic import BaseModel, Field


class PaymentDetailResponse(BaseModel):
    """Payment detail response model for PolicyListSF."""
    
    PaymentMethod: str = Field(..., description="Payment method")
    PayeeName: str = Field(..., description="Payee name")
    BankAccNo: str = Field(..., description="Bank account number")
    BankName: str = Field(..., description="Bank name")
    Primary: str = Field(..., description="Primary payment indicator")


class PolicyListResponse(BaseModel):
    """Policy response model for PolicyListSF endpoint."""
    
    MemberCode: str = Field(..., description="Member code")
    MemberStatus: str = Field(..., description="Member status")
    TitleTH: str = Field(..., description="Thai title")
    NameTH: str = Field(..., description="Thai name")
    SurnameTH: str = Field(..., description="Thai surname")
    TitleEN: str = Field(..., description="English title")
    NameEN: str = Field(..., description="English name")
    SurnameEN: str = Field(..., description="English surname")
    CitizenID: str = Field(..., description="Citizen ID")
    OtherID: str = Field(..., description="Other ID")
    StaffNo: str = Field(..., description="Staff number")
    InsurerCardNo: str = Field(..., description="Insurer card number")
    InsPreviousCardNo: str = Field(..., description="Previous insurer card number")
    PolicyNo: str = Field(..., description="Policy number")
    CertificateNo: str = Field(..., description="Certificate number")
    MemberType: str = Field(..., description="Member type")
    PrincipleMemberCode: str = Field(..., description="Principal member code")
    PrincipleName: str = Field(..., description="Principal name")
    VIP: str = Field(..., description="VIP status")
    VIPRemarks: str = Field(..., description="VIP remarks")
    CardType: str = Field(..., description="Card type")
    Language: str = Field(..., description="Language preference")
    InsurerCode: str = Field(..., description="Insurer code")
    InsurerName: str = Field(..., description="Insurer name")
    InsurerNameEN: str = Field(..., description="Insurer name in English")
    CompanyCode: str = Field(..., description="Company code")
    CompanyName: str = Field(..., description="Company name")
    CompanyNameEN: str = Field(..., description="Company name in English")
    BirthDate: str = Field(..., description="Birth date")
    Gender: str = Field(..., description="Gender")
    Citizenship: str = Field(..., description="Citizenship")
    CountryCode: str = Field(..., description="Country code")
    PlanCode: str = Field(..., description="Plan code")
    PlanName: str = Field(..., description="Plan name")
    PlanEffFrom: str = Field(..., description="Plan effective from date")
    PlanEffTo: str = Field(..., description="Plan effective to date")
    Mobile: str = Field(..., description="Mobile number")
    Email: str = Field(..., description="Email address")
    PaymentDetail: List[PaymentDetailResponse] = Field(..., description="Payment details")


class PolicyDetailResponse(BaseModel):
    """Policy detail response model for PolicyDetailSF endpoint."""
    
    MainBenefitCode: str = Field(..., description="Main benefit code")
    MainBenefit: str = Field(..., description="Main benefit description")
    MainBenefitEN: str = Field(..., description="Main benefit description in English")
    MainPlanLimitDesc: str = Field(..., description="Main plan limit description")
    MainPlanAmount: str = Field(..., description="Main plan amount")
    MainPlanUnit1: str = Field(..., description="Main plan unit 1")
    MainPlanUnit2: str = Field(..., description="Main plan unit 2")
    MainPlanBalance: str = Field(..., description="Main plan balance")


class BenefitDetailResponse(BaseModel):
    """Benefit detail response model for PolicyDetailSF endpoint."""
    
    BenefitCode: str = Field(..., description="Benefit code")
    BenefitTH: str = Field(..., description="Benefit description in Thai")
    BenefitEN: str = Field(..., description="Benefit description in English")
    SubBenefitCode: str = Field(..., description="Sub-benefit code")
    SubBenefitTH: str = Field(..., description="Sub-benefit description in Thai")
    SubBenefitEN: str = Field(..., description="Sub-benefit description in English")
    LimitCode: str = Field(..., description="Limit code")
    LimitAmt: str = Field(..., description="Limit amount")
    LimitUnit: str = Field(..., description="Limit unit")
    BalanceCode: str = Field(..., description="Balance code")
    BalanceLimitAmt: str = Field(..., description="Balance limit amount")
    BalanceUnit: str = Field(..., description="Balance unit")
    ComSubLimitCode: str = Field(..., description="Combined sub-limit code")
    ComSubLimitAmt: str = Field(..., description="Combined sub-limit amount")
    ComSubLimitUnit: str = Field(..., description="Combined sub-limit unit")
    BalComSubLimitCode: str = Field(..., description="Balance combined sub-limit code")
    BalComSubLimitAmt: str = Field(..., description="Balance combined sub-limit amount")
    BalComSubLimitUnit: str = Field(..., description="Balance combined sub-limit unit")
    ComLimitCode: str = Field(..., description="Combined limit code")
    ComLimitAmt: str = Field(..., description="Combined limit amount")
    ComLimitUnit: str = Field(..., description="Combined limit unit")
    BalComLimitCode: str = Field(..., description="Balance combined limit code")
    BalComLimitAmt: str = Field(..., description="Balance combined limit amount")
    BalComLimitUnit: str = Field(..., description="Balance combined limit unit")


class ContractConditionResponse(BaseModel):
    """Contract condition response model for PolicyDetailSF endpoint."""
    
    ConditionType: str = Field(..., description="Condition type")
    ConditionApply: str = Field(..., description="Condition apply")
    EffFromDate: str = Field(..., description="Effective from date")
    EffToDate: str = Field(..., description="Effective to date")
    ConditionDetail: str = Field(..., description="Condition detail")
    Action: str = Field(..., description="Action")
    SourceCondition: str = Field(..., description="Source condition")
    Remarks: str = Field(..., description="Remarks")
    CreateBy: str = Field(..., description="Created by")
    CreateDateTime: str = Field(..., description="Create date time")
    ModifiedBy: str = Field(..., description="Modified by")
    ModifiedDateTime: str = Field(..., description="Modified date time")


class MemberConditionResponse(BaseModel):
    """Member condition response model for PolicyDetailSF endpoint."""
    
    ConditionType: str = Field(..., description="Condition type")
    EffFromDate: str = Field(..., description="Effective from date")
    EffToDate: str = Field(..., description="Effective to date")
    ConditionDetail: str = Field(..., description="Condition detail")
    Action: str = Field(..., description="Action")
    SourceCondition: str = Field(..., description="Source condition")
    Remarks: str = Field(..., description="Remarks")
    CreateBy: str = Field(..., description="Created by")
    CreateDateTime: str = Field(..., description="Create date time")
    ModifiedBy: str = Field(..., description="Modified by")
    ModifiedDateTime: str = Field(..., description="Modified date time")


class ClaimHistoryResponse(BaseModel):
    """Claim history response model for PolicyDetailSF and ClaimListSF endpoints."""
    
    ClaimNo: str = Field(..., description="Claim number")
    MainBenefit: str = Field(..., description="Main benefit")
    ClaimSource: str = Field(..., description="Claim source")
    ClaimType: str = Field(..., description="Claim type")
    PatientNameTH: str = Field(..., description="Patient name in Thai")
    PatientNameEN: str = Field(..., description="Patient name in English")
    ClaimStatus: str = Field(..., description="Claim status")
    CitizenID: str = Field(..., description="Citizen ID")
    VisitDate: str = Field(..., description="Visit date")
    DischargeDate: str = Field(..., description="Discharge date")
    AccidentDate: str = Field(..., description="Accident date")
    IncurredAmt: str = Field(..., description="Incurred amount")
    PayableAmt: str = Field(..., description="Payable amount")
    ContractNo: str = Field(..., description="Contract number")
    InsurerCode: str = Field(..., description="Insurer code")
    InsurerTH: str = Field(..., description="Insurer name in Thai")
    InsurerEN: str = Field(..., description="Insurer name in English")
    CompanyCode: str = Field(..., description="Company code")
    CompanyTH: str = Field(..., description="Company name in Thai")
    CompanyEN: str = Field(..., description="Company name in English")
    CardType: str = Field(..., description="Card type")
    PolicyNo: str = Field(..., description="Policy number")
    ProviderCode: str = Field(..., description="Provider code")
    ProviderTH: str = Field(..., description="Provider name in Thai")
    ProviderEN: str = Field(..., description="Provider name in English")
    BatchNo: str = Field(..., description="Batch number")
    DiagCode: str = Field(..., description="Diagnosis code")
    DiagTH: str = Field(..., description="Diagnosis in Thai")
    DiagEN: str = Field(..., description="Diagnosis in English")
    PaymentDate: str = Field(..., description="Payment date")
    MemberCode: str = Field(..., description="Member code")


class PolicyDetailSFResponse(BaseModel):
    """Complete response model for PolicyDetailSF endpoint."""
    
    ListPolicyDetail: List[PolicyDetailResponse] = Field(..., description="Policy details")
    BenefitList: List[BenefitDetailResponse] = Field(..., description="Benefit details")
    ListContractCondition: List[ContractConditionResponse] = Field(..., description="Contract conditions")
    ListMemberCondition: List[MemberConditionResponse] = Field(..., description="Member conditions")
    ListClaimHistory: List[ClaimHistoryResponse] = Field(..., description="Claim history")


# Type aliases for endpoint responses
PolicyListSFResponse = List[PolicyListResponse]
ClaimListSFResponse = List[ClaimHistoryResponse]
