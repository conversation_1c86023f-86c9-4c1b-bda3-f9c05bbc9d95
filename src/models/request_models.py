"""
Pydantic request models for API endpoints.
Defines request validation models for PolicyListSF, PolicyDetailSF, and ClaimListSF endpoints.
"""

from typing import Optional
from pydantic import BaseModel, Field, model_validator
from src.utils.exceptions import ValidationError


class PolicyListSFRequest(BaseModel):
    """
    Request model for PolicyListSF endpoint.
    
    Supports 9 different parameter combinations:
    1. INSURER_CODE + CITIZEN_ID
    2. INSURER_CODE + POLICY_NO + NAME_TH
    3. INSURER_CODE + POLICY_NO + NAME_EN
    4. INSURER_CODE + CERTIFICATE_NO + NAME_TH
    5. INSURER_CODE + CERTIFICATE_NO + NAME_EN
    6. INSURER_CODE + STAFF_NO + NAME_TH
    7. INSURER_CODE + STAFF_NO + NAME_EN
    8. INSURER_CODE + OTHER_ID
    9. INSURER_CODE + NAME_TH
    10. INSURER_CODE + NAME_EN
    """
    
    INSURER_CODE: str = Field(..., description="Insurance company code (required)")
    CITIZEN_ID: Optional[str] = Field(None, description="Citizen identification number")
    POLICY_NO: Optional[str] = Field(None, description="Policy number")
    CERTIFICATE_NO: Optional[str] = Field(None, description="Certificate number")
    STAFF_NO: Optional[str] = Field(None, description="Staff number")
    OTHER_ID: Optional[str] = Field(None, description="Other identification")
    NAME_TH: Optional[str] = Field(None, description="Thai name (exact match, case-sensitive)")
    NAME_EN: Optional[str] = Field(None, description="English name (exact match, case-sensitive)")

    @model_validator(mode='after')
    def validate_parameter_combinations(self):
        """
        Validate that the request contains one of the allowed parameter combinations.
        """
        # Check for valid parameter combinations
        valid_combinations = [
            # 1. INSURER_CODE + CITIZEN_ID
            (self.CITIZEN_ID is not None and 
             all(x is None for x in [self.POLICY_NO, self.CERTIFICATE_NO, self.STAFF_NO, self.OTHER_ID, self.NAME_TH, self.NAME_EN])),
            
            # 2. INSURER_CODE + POLICY_NO + NAME_TH
            (self.POLICY_NO is not None and self.NAME_TH is not None and 
             all(x is None for x in [self.CITIZEN_ID, self.CERTIFICATE_NO, self.STAFF_NO, self.OTHER_ID, self.NAME_EN])),
            
            # 3. INSURER_CODE + POLICY_NO + NAME_EN
            (self.POLICY_NO is not None and self.NAME_EN is not None and 
             all(x is None for x in [self.CITIZEN_ID, self.CERTIFICATE_NO, self.STAFF_NO, self.OTHER_ID, self.NAME_TH])),
            
            # 4. INSURER_CODE + CERTIFICATE_NO + NAME_TH
            (self.CERTIFICATE_NO is not None and self.NAME_TH is not None and 
             all(x is None for x in [self.CITIZEN_ID, self.POLICY_NO, self.STAFF_NO, self.OTHER_ID, self.NAME_EN])),
            
            # 5. INSURER_CODE + CERTIFICATE_NO + NAME_EN
            (self.CERTIFICATE_NO is not None and self.NAME_EN is not None and 
             all(x is None for x in [self.CITIZEN_ID, self.POLICY_NO, self.STAFF_NO, self.OTHER_ID, self.NAME_TH])),
            
            # 6. INSURER_CODE + STAFF_NO + NAME_TH
            (self.STAFF_NO is not None and self.NAME_TH is not None and 
             all(x is None for x in [self.CITIZEN_ID, self.POLICY_NO, self.CERTIFICATE_NO, self.OTHER_ID, self.NAME_EN])),
            
            # 7. INSURER_CODE + STAFF_NO + NAME_EN
            (self.STAFF_NO is not None and self.NAME_EN is not None and 
             all(x is None for x in [self.CITIZEN_ID, self.POLICY_NO, self.CERTIFICATE_NO, self.OTHER_ID, self.NAME_TH])),
            
            # 8. INSURER_CODE + OTHER_ID
            (self.OTHER_ID is not None and 
             all(x is None for x in [self.CITIZEN_ID, self.POLICY_NO, self.CERTIFICATE_NO, self.STAFF_NO, self.NAME_TH, self.NAME_EN])),
            
            # 9. INSURER_CODE + NAME_TH
            (self.NAME_TH is not None and 
             all(x is None for x in [self.CITIZEN_ID, self.POLICY_NO, self.CERTIFICATE_NO, self.STAFF_NO, self.OTHER_ID, self.NAME_EN])),
            
            # 10. INSURER_CODE + NAME_EN
            (self.NAME_EN is not None and 
             all(x is None for x in [self.CITIZEN_ID, self.POLICY_NO, self.CERTIFICATE_NO, self.STAFF_NO, self.OTHER_ID, self.NAME_TH]))
        ]
        
        if not any(valid_combinations):
            raise ValidationError(
                "Invalid parameter combination. Must provide INSURER_CODE with one of: "
                "CITIZEN_ID, (POLICY_NO + NAME_TH), (POLICY_NO + NAME_EN), "
                "(CERTIFICATE_NO + NAME_TH), (CERTIFICATE_NO + NAME_EN), "
                "(STAFF_NO + NAME_TH), (STAFF_NO + NAME_EN), OTHER_ID, NAME_TH, NAME_EN"
            )
        
        return self


class PolicyDetailSFRequest(BaseModel):
    """
    Request model for PolicyDetailSF endpoint.
    
    Requires only MEMBER_CODE parameter.
    """
    
    MEMBER_CODE: str = Field(..., description="Member code (required)")


class ClaimListSFRequest(BaseModel):
    """
    Request model for ClaimListSF endpoint.
    
    Supports 2 parameter combinations:
    1. MEMBER_CODE
    2. INSURER_CODE + CITIZEN_ID
    """
    
    MEMBER_CODE: Optional[str] = Field(None, description="Member code")
    INSURER_CODE: Optional[str] = Field(None, description="Insurance company code")
    CITIZEN_ID: Optional[str] = Field(None, description="Citizen identification number")

    @model_validator(mode='after')
    def validate_parameter_combinations(self):
        """
        Validate that the request contains one of the allowed parameter combinations.
        """
        # Check for valid parameter combinations
        valid_combinations = [
            # 1. MEMBER_CODE only
            (self.MEMBER_CODE is not None and 
             all(x is None for x in [self.INSURER_CODE, self.CITIZEN_ID])),
            
            # 2. INSURER_CODE + CITIZEN_ID
            (self.INSURER_CODE is not None and self.CITIZEN_ID is not None and 
             self.MEMBER_CODE is None)
        ]
        
        if not any(valid_combinations):
            raise ValidationError(
                "Invalid parameter combination. Must provide either MEMBER_CODE or (INSURER_CODE + CITIZEN_ID)"
            )
        
        return self
