"""
CSV data loading and initialization for TPA API.
Handles loading and parsing of all CSV data files with proper error handling.
"""

import csv
import os
from typing import List, Dict, Any
from pathlib import Path

from config.settings import get_settings
from src.utils.logger import get_logger
from src.utils.exceptions import DataLoadingError
from src.models.data_models import (
    PolicyRecord,
    PaymentDetail,
    PolicyDetail,
    BenefitRecord,
    ContractCondition,
    MemberCondition,
    ClaimRecord
)

logger = get_logger(__name__)


class MockDataLoader:
    """Handles loading and parsing of CSV data files."""
    
    def __init__(self):
        """Initialize the data loader with settings."""
        self.settings = get_settings()
        self.csv_path = Path(self.settings.csv_data_path)
        
        # Data storage
        self.policies: List[PolicyRecord] = []
        self.payment_details: List[PaymentDetail] = []
        self.policy_details: List[PolicyDetail] = []
        self.benefits: List[BenefitRecord] = []
        self.contract_conditions: List[ContractCondition] = []
        self.member_conditions: List[MemberCondition] = []
        self.claims: List[ClaimRecord] = []
        
        # Load status
        self._loaded = False
    
    def load_all_data(self) -> None:
        """Load all CSV data files."""
        if self._loaded:
            logger.info("Data already loaded, skipping reload")
            return
        
        logger.info("Starting to load all CSV data files")
        
        try:
            # Load each CSV file
            self.policies = self._load_csv_file("policies.csv", PolicyRecord)
            self.payment_details = self._load_csv_file("payment_details.csv", PaymentDetail)
            self.policy_details = self._load_csv_file("policy_details.csv", PolicyDetail)
            self.benefits = self._load_csv_file("benefits.csv", BenefitRecord)
            self.contract_conditions = self._load_csv_file("contract_conditions.csv", ContractCondition)
            self.member_conditions = self._load_csv_file("member_conditions.csv", MemberCondition)
            self.claims = self._load_csv_file("claims.csv", ClaimRecord)
            
            self._loaded = True
            
            # Log loading summary
            logger.info("Successfully loaded all CSV data", extra={
                "policies_count": len(self.policies),
                "payment_details_count": len(self.payment_details),
                "policy_details_count": len(self.policy_details),
                "benefits_count": len(self.benefits),
                "contract_conditions_count": len(self.contract_conditions),
                "member_conditions_count": len(self.member_conditions),
                "claims_count": len(self.claims)
            })
            
        except Exception as e:
            logger.error(f"Failed to load CSV data: {str(e)}")
            raise DataLoadingError(f"Failed to load CSV data: {str(e)}")
    
    def _load_csv_file(self, filename: str, model_class) -> List[Any]:
        """
        Load a single CSV file and parse it using the specified model.
        
        Args:
            filename: Name of the CSV file to load
            model_class: Pydantic model class to parse the data
            
        Returns:
            List of parsed model instances
            
        Raises:
            DataLoadingError: If file loading or parsing fails
        """
        file_path = self.csv_path / filename
        
        if not file_path.exists():
            raise DataLoadingError(f"CSV file not found: {file_path}")
        
        logger.info(f"Loading CSV file: {filename}")
        
        try:
            records = []
            with open(file_path, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                
                for row_num, row in enumerate(reader, start=2):  # Start at 2 (header is row 1)
                    try:
                        # Parse row using the model
                        record = model_class(**row)
                        records.append(record)
                    except Exception as e:
                        logger.warning(
                            f"Failed to parse row {row_num} in {filename}: {str(e)}",
                            extra={"row_data": row, "error": str(e)}
                        )
                        # Continue processing other rows
                        continue
            
            logger.info(f"Successfully loaded {len(records)} records from {filename}")
            return records
            
        except Exception as e:
            raise DataLoadingError(f"Failed to load {filename}: {str(e)}")
    
    def get_policies(self) -> List[PolicyRecord]:
        """Get all policy records."""
        self._ensure_loaded()
        return self.policies
    
    def get_payment_details(self) -> List[PaymentDetail]:
        """Get all payment detail records."""
        self._ensure_loaded()
        return self.payment_details
    
    def get_policy_details(self) -> List[PolicyDetail]:
        """Get all policy detail records."""
        self._ensure_loaded()
        return self.policy_details
    
    def get_benefits(self) -> List[BenefitRecord]:
        """Get all benefit records."""
        self._ensure_loaded()
        return self.benefits
    
    def get_contract_conditions(self) -> List[ContractCondition]:
        """Get all contract condition records."""
        self._ensure_loaded()
        return self.contract_conditions
    
    def get_member_conditions(self) -> List[MemberCondition]:
        """Get all member condition records."""
        self._ensure_loaded()
        return self.member_conditions
    
    def get_claims(self) -> List[ClaimRecord]:
        """Get all claim records."""
        self._ensure_loaded()
        return self.claims
    
    def _ensure_loaded(self) -> None:
        """Ensure data is loaded before accessing it."""
        if not self._loaded:
            self.load_all_data()


# Global data loader instance
_data_loader = None


def get_data_loader() -> MockDataLoader:
    """Get the global data loader instance."""
    global _data_loader
    if _data_loader is None:
        _data_loader = MockDataLoader()
    return _data_loader


def initialize_data() -> None:
    """Initialize and load all data."""
    loader = get_data_loader()
    loader.load_all_data()
