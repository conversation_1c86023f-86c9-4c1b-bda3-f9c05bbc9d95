"""
Claim data management and indexing for TPA API.
Provides efficient access to claim data with proper filtering and validation.
"""

from typing import List, Dict, Optional, Any

from src.data.mock_data import get_data_loader
from src.models.data_models import <PERSON><PERSON><PERSON><PERSON><PERSON>ord, ContractCondition, MemberCondition
from src.utils.helpers import create_lookup_index, validate_claim_status
from src.utils.logger import get_logger

logger = get_logger(__name__)


class ClaimDataManager:
    """Manages claim data with efficient indexing and filtering."""

    def __init__(self):
        """Initialize the claim data manager."""
        self.data_loader = get_data_loader()

        # Indexes for fast lookups
        self._claims_by_member_code: Dict[str, List[Dict[str, Any]]] = {}
        self._claims_by_citizen_id: Dict[str, List[Dict[str, Any]]] = {}
        self._claims_by_insurer_code: Dict[str, List[Dict[str, Any]]] = {}

        self._contract_conditions_by_member_code: Dict[str, List[Dict[str, Any]]] = {}
        self._member_conditions_by_member_code: Dict[str, List[Dict[str, Any]]] = {}

        # Index status
        self._indexed = False

    def _build_indexes(self) -> None:
        """Build all indexes for fast data access."""
        if self._indexed:
            return

        logger.info("Building claim data indexes")

        # Get all data
        claims = self.data_loader.get_claims()
        contract_conditions = self.data_loader.get_contract_conditions()
        member_conditions = self.data_loader.get_member_conditions()

        # Filter claims by valid status
        valid_claims = []
        for claim in claims:
            claim_dict = claim.model_dump()
            if validate_claim_status(claim_dict.get("ClaimStatus", "")):
                valid_claims.append(claim_dict)
            else:
                logger.warning(
                    f"Skipping claim with invalid status: {claim_dict.get('ClaimStatus')}",
                    extra={"claim_no": claim_dict.get("ClaimNo")},
                )

        # Build claim indexes
        self._claims_by_member_code = create_lookup_index(valid_claims, "MemberCode")
        self._claims_by_citizen_id = create_lookup_index(valid_claims, "CitizenID")
        self._claims_by_insurer_code = create_lookup_index(valid_claims, "InsurerCode")

        # Build condition indexes
        self._contract_conditions_by_member_code = create_lookup_index(
            [condition.model_dump() for condition in contract_conditions], "MemberCode"
        )
        self._member_conditions_by_member_code = create_lookup_index(
            [condition.model_dump() for condition in member_conditions], "MemberCode"
        )

        self._indexed = True
        logger.info(
            f"Claim data indexes built successfully. Valid claims: {len(valid_claims)}"
        )

    def get_claims_by_member_code(self, member_code: str) -> List[Dict[str, Any]]:
        """
        Get claims by member code.

        Args:
            member_code: Member code to search for

        Returns:
            List of claim records with valid statuses
        """
        self._build_indexes()
        return self._claims_by_member_code.get(member_code, [])

    def get_claims_by_citizen_id(self, citizen_id: str) -> List[Dict[str, Any]]:
        """
        Get claims by citizen ID.

        Args:
            citizen_id: Citizen ID to search for

        Returns:
            List of claim records with valid statuses
        """
        self._build_indexes()
        return self._claims_by_citizen_id.get(citizen_id, [])

    def get_claims_by_insurer_and_citizen(
        self, insurer_code: str, citizen_id: str
    ) -> List[Dict[str, Any]]:
        """
        Get claims by insurer code and citizen ID combination.

        Args:
            insurer_code: Insurer code to filter by
            citizen_id: Citizen ID to filter by

        Returns:
            List of matching claim records
        """
        self._build_indexes()

        # Get claims by insurer code
        insurer_claims = self._claims_by_insurer_code.get(insurer_code, [])

        # Filter by citizen ID
        matching_claims = [
            claim for claim in insurer_claims if claim.get("CitizenID") == citizen_id
        ]

        return matching_claims

    def get_contract_conditions_by_member_code(
        self, member_code: str
    ) -> List[Dict[str, Any]]:
        """
        Get contract conditions by member code.

        Args:
            member_code: Member code to search for

        Returns:
            List of contract condition records
        """
        self._build_indexes()
        return self._contract_conditions_by_member_code.get(member_code, [])

    def get_member_conditions_by_member_code(
        self, member_code: str
    ) -> List[Dict[str, Any]]:
        """
        Get member conditions by member code.

        Args:
            member_code: Member code to search for

        Returns:
            List of member condition records
        """
        self._build_indexes()
        return self._member_conditions_by_member_code.get(member_code, [])

    def get_all_conditions_by_member_code(
        self, member_code: str
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get all conditions (contract and member) by member code.

        Args:
            member_code: Member code to search for

        Returns:
            Dictionary with contract_conditions and member_conditions lists
        """
        return {
            "contract_conditions": self.get_contract_conditions_by_member_code(
                member_code
            ),
            "member_conditions": self.get_member_conditions_by_member_code(member_code),
        }

    def get_valid_claim_statuses(self) -> List[str]:
        """
        Get list of valid claim statuses.

        Returns:
            List of allowed claim statuses
        """
        return [
            "Approved",
            "Authorized",
            "Open",
            "Paid",
            "Pending",
            "Pending For Approval",
            "Rejected",
        ]

    def validate_claim_status(self, status: str) -> bool:
        """
        Validate if a claim status is allowed.

        Args:
            status: Claim status to validate

        Returns:
            True if status is valid, False otherwise
        """
        return validate_claim_status(status)


# Global claim data manager instance
_claim_manager = None


def get_claim_manager() -> ClaimDataManager:
    """Get the global claim data manager instance."""
    global _claim_manager
    if _claim_manager is None:
        _claim_manager = ClaimDataManager()
    return _claim_manager
