"""
Policy data management and indexing for TPA API.
Provides efficient access to policy-related data with proper indexing and filtering.
"""

from typing import List, Dict, Optional, Any
from datetime import date

from src.data.mock_data import get_data_loader
from src.models.data_models import (
    PolicyRecord,
    PaymentDetail,
    PolicyDetail,
    BenefitRecord,
)
from src.utils.helpers import (
    create_lookup_index,
    create_unique_index,
    exact_match,
    is_policy_active,
    get_current_date_thailand,
)
from src.utils.logger import get_logger

logger = get_logger(__name__)


class PolicyDataManager:
    """Manages policy data with efficient indexing and filtering."""

    def __init__(self):
        """Initialize the policy data manager."""
        self.data_loader = get_data_loader()

        # Indexes for fast lookups
        self._policy_by_member_code: Dict[str, PolicyRecord] = {}
        self._policy_by_citizen_id: Dict[str, PolicyRecord] = {}
        self._policies_by_insurer_code: Dict[str, List[PolicyRecord]] = {}
        self._policies_by_company_code: Dict[str, List[PolicyRecord]] = {}
        self._policies_by_policy_no: Dict[str, List[PolicyRecord]] = {}

        self._payment_details_by_member_code: Dict[str, List[PaymentDetail]] = {}
        self._policy_details_by_member_code: Dict[str, PolicyDetail] = {}
        self._benefits_by_member_code: Dict[str, List[BenefitRecord]] = {}

        # Index status
        self._indexed = False

    def _build_indexes(self) -> None:
        """Build all indexes for fast data access."""
        if self._indexed:
            return

        logger.info("Building policy data indexes")

        # Get all data
        policies = self.data_loader.get_policies()
        payment_details = self.data_loader.get_payment_details()
        policy_details = self.data_loader.get_policy_details()
        benefits = self.data_loader.get_benefits()

        # Build policy indexes
        self._policy_by_member_code = create_unique_index(
            [policy.model_dump() for policy in policies], "MemberCode"
        )
        self._policy_by_citizen_id = create_unique_index(
            [policy.model_dump() for policy in policies], "CitizenID"
        )
        self._policies_by_insurer_code = create_lookup_index(
            [policy.model_dump() for policy in policies], "InsurerCode"
        )
        self._policies_by_company_code = create_lookup_index(
            [policy.model_dump() for policy in policies], "CompanyCode"
        )
        self._policies_by_policy_no = create_lookup_index(
            [policy.model_dump() for policy in policies], "PolicyNo"
        )

        # Build related data indexes
        self._payment_details_by_member_code = create_lookup_index(
            [detail.model_dump() for detail in payment_details], "MemberCode"
        )
        self._policy_details_by_member_code = create_unique_index(
            [detail.model_dump() for detail in policy_details], "MemberCode"
        )
        self._benefits_by_member_code = create_lookup_index(
            [benefit.model_dump() for benefit in benefits], "MemberCode"
        )

        self._indexed = True
        logger.info("Policy data indexes built successfully")

    def get_policy_by_member_code(self, member_code: str) -> Optional[Dict[str, Any]]:
        """
        Get policy by member code.

        Args:
            member_code: Member code to search for

        Returns:
            Policy record or None if not found
        """
        self._build_indexes()
        return self._policy_by_member_code.get(member_code)

    def get_policy_by_citizen_id(self, citizen_id: str) -> Optional[Dict[str, Any]]:
        """
        Get policy by citizen ID.

        Args:
            citizen_id: Citizen ID to search for

        Returns:
            Policy record or None if not found
        """
        self._build_indexes()
        return self._policy_by_citizen_id.get(citizen_id)

    def get_active_policies_by_insurer_code(
        self, insurer_code: str
    ) -> List[Dict[str, Any]]:
        """
        Get active policies by insurer code.

        Args:
            insurer_code: Insurer code to filter by

        Returns:
            List of active policy records
        """
        self._build_indexes()
        policies = self._policies_by_insurer_code.get(insurer_code, [])

        # Filter for active policies only
        active_policies = []
        for policy in policies:
            if is_policy_active(
                policy.get("PlanEffFrom", ""), policy.get("PlanEffTo", "")
            ):
                active_policies.append(policy)

        return active_policies

    def filter_policies_by_criteria(
        self,
        insurer_code: str,
        company_code: Optional[str] = None,
        policy_no: Optional[str] = None,
        citizen_id: Optional[str] = None,
        name_th: Optional[str] = None,
        name_en: Optional[str] = None,
        member_code: Optional[str] = None,
        staff_no: Optional[str] = None,
        insurer_card_no: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        Filter policies by multiple criteria.

        Args:
            insurer_code: Required insurer code
            company_code: Optional company code filter
            policy_no: Optional policy number filter
            citizen_id: Optional citizen ID filter
            name_th: Optional Thai name filter (exact match)
            name_en: Optional English name filter (exact match)
            member_code: Optional member code filter
            staff_no: Optional staff number filter
            insurer_card_no: Optional insurer card number filter

        Returns:
            List of matching active policy records
        """
        self._build_indexes()

        # Start with active policies for the insurer
        policies = self.get_active_policies_by_insurer_code(insurer_code)

        # Apply additional filters
        if company_code:
            policies = [p for p in policies if p.get("CompanyCode") == company_code]

        if policy_no:
            policies = [p for p in policies if p.get("PolicyNo") == policy_no]

        if citizen_id:
            policies = [p for p in policies if p.get("CitizenID") == citizen_id]

        if name_th:
            policies = [
                p for p in policies if exact_match(p.get("NameTH", ""), name_th)
            ]

        if name_en:
            policies = [
                p for p in policies if exact_match(p.get("NameEN", ""), name_en)
            ]

        if member_code:
            policies = [p for p in policies if p.get("MemberCode") == member_code]

        if staff_no:
            policies = [p for p in policies if p.get("StaffNo") == staff_no]

        if insurer_card_no:
            policies = [
                p for p in policies if p.get("InsurerCardNo") == insurer_card_no
            ]

        return policies

    def get_payment_details_by_member_code(
        self, member_code: str
    ) -> List[Dict[str, Any]]:
        """
        Get payment details by member code.

        Args:
            member_code: Member code to search for

        Returns:
            List of payment detail records
        """
        self._build_indexes()
        return self._payment_details_by_member_code.get(member_code, [])

    def get_policy_detail_by_member_code(
        self, member_code: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get policy detail by member code.

        Args:
            member_code: Member code to search for

        Returns:
            Policy detail record or None if not found
        """
        self._build_indexes()
        return self._policy_details_by_member_code.get(member_code)

    def get_benefits_by_member_code(self, member_code: str) -> List[Dict[str, Any]]:
        """
        Get benefits by member code.

        Args:
            member_code: Member code to search for

        Returns:
            List of benefit records
        """
        self._build_indexes()
        return self._benefits_by_member_code.get(member_code, [])


# Global policy data manager instance
_policy_manager = None


def get_policy_manager() -> PolicyDataManager:
    """Get the global policy data manager instance."""
    global _policy_manager
    if _policy_manager is None:
        _policy_manager = PolicyDataManager()
    return _policy_manager
