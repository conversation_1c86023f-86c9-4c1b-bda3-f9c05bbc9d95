"""
FastAPI application factory and startup configuration for TPA API.
Handles application initialization, middleware setup, and route registration.
"""

import logging
from datetime import datetime
from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError

from config.settings import get_settings
from src.utils.logger import get_logger, setup_logging
from src.utils.exceptions import (
    TPAAPIException,
    EXCEPTION_HANDLERS,
    validation_error_to_http,
)
from src.data.mock_data import initialize_data
from src.api import (
    policy_list_router,
    policy_detail_router,
    claim_list_router,
)

# Initialize settings and logger
settings = get_settings()
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager.
    Handles startup and shutdown events.
    """
    # Startup
    logger.info("Starting TPA API application")

    try:
        # Setup logging
        setup_logging()
        logger.info("Logging configured successfully")

        # Initialize data
        logger.info("Initializing CSV data...")
        initialize_data()
        logger.info("Data initialization completed successfully")

        logger.info(
            f"TPA API startup completed",
            extra={
                "app_name": settings.app_name,
                "app_version": settings.app_version,
                "debug": settings.debug,
                "api_prefix": settings.api_prefix,
            },
        )

    except Exception as e:
        logger.error(f"Failed to initialize application: {str(e)}")
        raise

    yield

    # Shutdown
    logger.info("Shutting down TPA API application")


def create_app() -> FastAPI:
    """
    Create and configure FastAPI application.

    Returns:
        Configured FastAPI application instance
    """
    # Create FastAPI app with lifespan
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="A FastAPI-based REST API for insurance policy and claims management",
        debug=settings.debug,
        lifespan=lifespan,
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE"],
        allow_headers=["*"],
    )

    # Add custom exception handlers
    setup_exception_handlers(app)

    # Include API routers
    app.include_router(
        policy_list_router, prefix=settings.api_prefix, tags=["Policy Management"]
    )

    app.include_router(
        policy_detail_router, prefix=settings.api_prefix, tags=["Policy Management"]
    )

    app.include_router(
        claim_list_router, prefix=settings.api_prefix, tags=["Claim Management"]
    )

    # Add health check endpoint
    @app.get("/health", tags=["Health"])
    async def health_check():
        """
        Health check endpoint.

        Returns:
            Health status information
        """
        return {
            "status": "healthy",
            "app_name": settings.app_name,
            "app_version": settings.app_version,
            "timestamp": datetime.utcnow().isoformat() + "Z",
        }

    # Add root endpoint
    @app.get("/", tags=["Root"])
    async def root():
        """
        Root endpoint with API information.

        Returns:
            API information and available endpoints
        """
        return {
            "message": f"Welcome to {settings.app_name}",
            "version": settings.app_version,
            "docs_url": "/docs",
            "health_url": "/health",
            "api_endpoints": {
                "policy_list": f"{settings.api_prefix}/PolicyListSF",
                "policy_detail": f"{settings.api_prefix}/PolicyDetailSF",
                "claim_list": f"{settings.api_prefix}/ClaimListSF",
            },
        }

    return app


def setup_exception_handlers(app: FastAPI):
    """
    Setup custom exception handlers for the FastAPI application.

    Args:
        app: FastAPI application instance
    """

    @app.exception_handler(TPAAPIException)
    async def tpa_exception_handler(request: Request, exc: TPAAPIException):
        """Handle custom TPA API exceptions."""
        logger.error(
            f"TPA API Exception: {str(exc)}",
            extra={
                "exception_type": type(exc).__name__,
                "path": request.url.path,
                "method": request.method,
            },
        )

        # Use existing exception handlers
        for exception_type, handler in EXCEPTION_HANDLERS.items():
            if isinstance(exc, exception_type):
                http_exc = handler(exc)
                return JSONResponse(
                    status_code=http_exc.status_code, content=http_exc.detail
                )

        # Fallback for unhandled TPA exceptions
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal Server Error",
                "message": str(exc),
                "details": {},
            },
        )

    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(
        request: Request, exc: RequestValidationError
    ):
        """Handle FastAPI request validation errors."""
        logger.warning(
            f"Request validation error: {str(exc)}",
            extra={
                "path": request.url.path,
                "method": request.method,
                "errors": exc.errors(),
            },
        )

        return JSONResponse(
            status_code=422,
            content={
                "error": "Validation Error",
                "message": "Request validation failed",
                "details": exc.errors(),
            },
        )

    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle unexpected exceptions."""
        logger.error(
            f"Unexpected exception: {str(exc)}",
            extra={
                "exception_type": type(exc).__name__,
                "path": request.url.path,
                "method": request.method,
            },
        )

        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal Server Error",
                "message": "An unexpected error occurred",
                "details": {},
            },
        )


# Create the application instance
app = create_app()


if __name__ == "__main__":
    import uvicorn

    logger.info(
        f"Starting {settings.app_name} server",
        extra={
            "host": settings.host,
            "port": settings.port,
            "debug": settings.debug,
        },
    )

    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )
