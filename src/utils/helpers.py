"""
Utility functions for TPA API.
Provides helper functions for date handling, data processing, and common operations.
"""

from datetime import datetime, date, timezone
from typing import Any, Dict, List, Optional, Union
import re
from config.settings import get_thailand_timezone
from src.utils.exceptions import DateParsingError


def parse_date(date_string: str, field_name: str = "date") -> date:
    """
    Parse date string in YYYY-MM-DD format.
    
    Args:
        date_string: Date string to parse
        field_name: Name of the field for error reporting
        
    Returns:
        Parsed date object
        
    Raises:
        DateParsingError: If date parsing fails
    """
    if not date_string or not date_string.strip():
        raise DateParsingError(date_string, field_name, {"reason": "Empty date string"})
    
    try:
        return datetime.strptime(date_string.strip(), "%Y-%m-%d").date()
    except ValueError as e:
        raise DateParsingError(
            date_string, 
            field_name, 
            {"reason": str(e), "expected_format": "YYYY-MM-DD"}
        )


def parse_datetime(datetime_string: str, field_name: str = "datetime") -> datetime:
    """
    Parse datetime string in YYYY-MM-DD HH:MM:SS format.
    
    Args:
        datetime_string: Datetime string to parse
        field_name: Name of the field for error reporting
        
    Returns:
        Parsed datetime object in Thailand timezone
        
    Raises:
        DateParsingError: If datetime parsing fails
    """
    if not datetime_string or not datetime_string.strip():
        raise DateParsingError(datetime_string, field_name, {"reason": "Empty datetime string"})
    
    try:
        # Parse the datetime string
        dt = datetime.strptime(datetime_string.strip(), "%Y-%m-%d %H:%M:%S")
        # Localize to Thailand timezone
        return dt.replace(tzinfo=get_thailand_timezone())
    except ValueError as e:
        raise DateParsingError(
            datetime_string, 
            field_name, 
            {"reason": str(e), "expected_format": "YYYY-MM-DD HH:MM:SS"}
        )


def get_current_date_thailand() -> date:
    """
    Get current date in Thailand timezone.
    
    Returns:
        Current date in Thailand timezone
    """
    thailand_tz = get_thailand_timezone()
    return datetime.now(thailand_tz).date()


def is_policy_active(plan_eff_from: str, plan_eff_to: str) -> bool:
    """
    Check if a policy is currently active based on effective dates.
    
    Args:
        plan_eff_from: Policy effective from date string
        plan_eff_to: Policy effective to date string
        
    Returns:
        True if policy is active, False otherwise
    """
    try:
        eff_from = parse_date(plan_eff_from, "PlanEffFrom")
        eff_to = parse_date(plan_eff_to, "PlanEffTo")
        current_date = get_current_date_thailand()
        
        return eff_from <= current_date <= eff_to
    except DateParsingError:
        # If date parsing fails, consider policy inactive
        return False


def normalize_string(value: str) -> str:
    """
    Normalize string for comparison (trim whitespace).
    
    Args:
        value: String to normalize
        
    Returns:
        Normalized string
    """
    return value.strip() if value else ""


def exact_match(value1: str, value2: str) -> bool:
    """
    Perform exact case-sensitive string matching.
    
    Args:
        value1: First string to compare
        value2: Second string to compare
        
    Returns:
        True if strings match exactly, False otherwise
    """
    return normalize_string(value1) == normalize_string(value2)


def safe_get(dictionary: Dict[str, Any], key: str, default: Any = None) -> Any:
    """
    Safely get value from dictionary with default.
    
    Args:
        dictionary: Dictionary to get value from
        key: Key to look up
        default: Default value if key not found
        
    Returns:
        Value from dictionary or default
    """
    return dictionary.get(key, default)


def filter_empty_strings(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Filter out empty string values from dictionary.
    
    Args:
        data: Dictionary to filter
        
    Returns:
        Dictionary with empty strings removed
    """
    return {k: v for k, v in data.items() if v != ""}


def group_by_key(items: List[Dict[str, Any]], key: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    Group list of dictionaries by a specific key.
    
    Args:
        items: List of dictionaries to group
        key: Key to group by
        
    Returns:
        Dictionary with grouped items
    """
    groups: Dict[str, List[Dict[str, Any]]] = {}
    for item in items:
        group_key = item.get(key)
        if group_key:
            if group_key not in groups:
                groups[group_key] = []
            groups[group_key].append(item)
    return groups


def validate_claim_status(status: str) -> bool:
    """
    Validate if claim status is allowed.
    
    Args:
        status: Claim status to validate
        
    Returns:
        True if status is allowed, False otherwise
    """
    allowed_statuses = {
        "Approved",
        "Authorized",  # Corrected from "Authoried"
        "Open",
        "Paid",
        "Pending",
        "Pending For Approval",
        "Rejected"
    }
    return status in allowed_statuses


def create_lookup_index(items: List[Dict[str, Any]], key: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    Create a lookup index for fast searching.
    
    Args:
        items: List of items to index
        key: Key to create index on
        
    Returns:
        Dictionary index for fast lookups
    """
    index: Dict[str, List[Dict[str, Any]]] = {}
    for item in items:
        index_key = item.get(key)
        if index_key:
            if index_key not in index:
                index[index_key] = []
            index[index_key].append(item)
    return index


def create_unique_index(items: List[Dict[str, Any]], key: str) -> Dict[str, Dict[str, Any]]:
    """
    Create a unique lookup index (assumes one item per key).
    
    Args:
        items: List of items to index
        key: Key to create index on
        
    Returns:
        Dictionary index for unique lookups
    """
    index: Dict[str, Dict[str, Any]] = {}
    for item in items:
        index_key = item.get(key)
        if index_key:
            index[index_key] = item
    return index
