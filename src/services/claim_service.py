"""
Business logic service for claim-related operations.
Handles ClaimListSF endpoint logic.
"""

from typing import List, Dict, Any
from src.data.claim_data import get_claim_manager
from src.data.policy_data import get_policy_manager
from src.models.request_models import ClaimListSFRequest
from src.models.response_models import ClaimHistoryResponse
from src.services.validation_service import get_validation_service
from src.utils.exceptions import NotFoundError, BusinessLogicError, ValidationError
from src.utils.logger import get_logger

logger = get_logger(__name__)


class ClaimService:
    """Service for claim-related business logic."""

    def __init__(self):
        """Initialize the claim service."""
        self.claim_manager = get_claim_manager()
        self.policy_manager = get_policy_manager()
        self.validation_service = get_validation_service()

    def get_claim_list(self, params: Dict[str, Any]) -> List[ClaimHistoryResponse]:
        """
        Get claim list based on search parameters.

        Args:
            params: Request parameters dictionary

        Returns:
            List of ClaimHistoryResponse objects

        Raises:
            ValidationError: If parameters are invalid
            NotFoundError: If no data found
            BusinessLogicError: If business logic fails
        """
        try:
            # Validate request parameters
            request = self.validation_service.validate_claim_list_request(params)

            # Get search strategy
            strategy, search_params = (
                self.validation_service.get_claim_list_search_strategy(request)
            )

            logger.info(
                f"Executing ClaimListSF search with strategy: {strategy}",
                extra={"search_params": search_params},
            )

            # Execute search based on strategy
            claims = self._execute_claim_search(strategy, search_params)

            # Convert to response format
            response_list = []
            for claim in claims:
                try:
                    claim_response = self._convert_claim_to_response(claim)
                    response_list.append(claim_response)
                except Exception as e:
                    logger.warning(
                        f"Failed to convert claim to response: {str(e)}",
                        extra={"claim_no": claim.get("ClaimNo")},
                    )
                    continue

            logger.info(
                f"ClaimListSF search completed successfully",
                extra={"strategy": strategy, "results_count": len(response_list)},
            )

            return response_list

        except Exception as e:
            logger.error(f"ClaimListSF search failed: {str(e)}")
            if isinstance(e, (ValidationError, NotFoundError, BusinessLogicError)):
                raise
            raise BusinessLogicError(f"Claim search failed: {str(e)}")

    def _execute_claim_search(
        self, strategy: str, search_params: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Execute claim search based on strategy.

        Args:
            strategy: Search strategy name
            search_params: Search parameters

        Returns:
            List of claim records
        """
        if strategy == "member_code":
            # Search by member code
            member_code = search_params["member_code"]
            claims = self.claim_manager.get_claims_by_member_code(member_code)

            logger.debug(f"Found {len(claims)} claims for member code: {member_code}")

            return claims

        elif strategy == "insurer_citizen":
            # Search by insurer code + citizen ID
            insurer_code = search_params["insurer_code"]
            citizen_id = search_params["citizen_id"]

            # First, find the policy to get the member code
            policy = self.policy_manager.get_policy_by_citizen_id(citizen_id)
            if not policy:
                logger.info(
                    f"No policy found for citizen ID: {citizen_id}",
                    extra={"insurer_code": insurer_code},
                )
                return []

            # Verify the policy belongs to the specified insurer
            if policy.get("InsurerCode") != insurer_code:
                logger.info(
                    f"Policy found but insurer code mismatch. Expected: {insurer_code}, Found: {policy.get('InsurerCode')}",
                    extra={"citizen_id": citizen_id},
                )
                return []

            # Get claims for this member
            member_code = policy.get("MemberCode")
            claims = self.claim_manager.get_claims_by_member_code(member_code)

            logger.debug(
                f"Found {len(claims)} claims for citizen ID: {citizen_id} (member: {member_code})"
            )

            return claims

        else:
            logger.error(f"Unknown claim search strategy: {strategy}")
            return []

    def _convert_claim_to_response(self, claim: Dict[str, Any]) -> ClaimHistoryResponse:
        """
        Convert claim data to ClaimHistoryResponse.

        Args:
            claim: Claim data dictionary

        Returns:
            ClaimHistoryResponse object
        """
        return ClaimHistoryResponse(
            ClaimNo=claim.get("ClaimNo", ""),
            MainBenefit=claim.get("MainBenefit", ""),
            ClaimSource=claim.get("ClaimSource", ""),
            ClaimType=claim.get("ClaimType", ""),
            PatientNameTH=claim.get("PatientNameTH", ""),
            PatientNameEN=claim.get("PatientNameEN", ""),
            ClaimStatus=claim.get("ClaimStatus", ""),
            CitizenID=claim.get("CitizenID", ""),
            VisitDate=claim.get("VisitDate", ""),
            DischargeDate=claim.get("DischargeDate", ""),
            AccidentDate=claim.get("AccidentDate", ""),
            IncurredAmt=claim.get("IncurredAmt", ""),
            PayableAmt=claim.get("PayableAmt", ""),
            ContractNo=claim.get("ContractNo", ""),
            InsurerCode=claim.get("InsurerCode", ""),
            InsurerTH=claim.get("InsurerTH", ""),
            InsurerEN=claim.get("InsurerEN", ""),
            CompanyCode=claim.get("CompanyCode", ""),
            CompanyTH=claim.get("CompanyTH", ""),
            CompanyEN=claim.get("CompanyEN", ""),
            CardType=claim.get("CardType", ""),
            PolicyNo=claim.get("PolicyNo", ""),
            ProviderCode=claim.get("ProviderCode", ""),
            ProviderTH=claim.get("ProviderTH", ""),
            ProviderEN=claim.get("ProviderEN", ""),
            BatchNo=claim.get("BatchNo", ""),
            DiagCode=claim.get("DiagCode", ""),
            DiagTH=claim.get("DiagTH", ""),
            DiagEN=claim.get("DiagEN", ""),
            PaymentDate=claim.get("PaymentDate", ""),
            MemberCode=claim.get("MemberCode", ""),
        )

    def get_valid_claim_statuses(self) -> List[str]:
        """
        Get list of valid claim statuses.

        Returns:
            List of allowed claim statuses
        """
        return self.claim_manager.get_valid_claim_statuses()

    def validate_claim_status(self, status: str) -> bool:
        """
        Validate if a claim status is allowed.

        Args:
            status: Claim status to validate

        Returns:
            True if status is valid, False otherwise
        """
        return self.claim_manager.validate_claim_status(status)


# Global claim service instance
_claim_service = None


def get_claim_service() -> ClaimService:
    """Get the global claim service instance."""
    global _claim_service
    if _claim_service is None:
        _claim_service = ClaimService()
    return _claim_service
