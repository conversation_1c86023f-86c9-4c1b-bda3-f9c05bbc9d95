"""
Parameter validation service for TPA API endpoints.
Provides validation logic for PolicyListSF, PolicyDetailSF, and ClaimListSF endpoints.
"""

from typing import Dict, Any, Optional, Tuple
from src.models.request_models import (
    PolicyListSFRequest,
    PolicyDetailSFRequest,
    ClaimListSFRequest,
)
from src.utils.exceptions import ValidationError
from src.utils.logger import get_logger

logger = get_logger(__name__)


class ValidationService:
    """Service for validating API request parameters."""

    def validate_policy_list_request(self, params: Dict[str, Any]) -> PolicyListSFRequest:
        """
        Validate PolicyListSF request parameters.
        
        Args:
            params: Request parameters dictionary
            
        Returns:
            Validated PolicyListSFRequest object
            
        Raises:
            ValidationError: If validation fails
        """
        try:
            # Create and validate the request model
            request = PolicyListSFRequest(**params)
            
            logger.info(
                "PolicyListSF request validated successfully",
                extra={
                    "insurer_code": request.INSURER_CODE,
                    "has_citizen_id": request.CITIZEN_ID is not None,
                    "has_policy_no": request.POLICY_NO is not None,
                    "has_name_th": request.NAME_TH is not None,
                    "has_name_en": request.NAME_EN is not None,
                }
            )
            
            return request
            
        except Exception as e:
            logger.error(
                f"PolicyListSF request validation failed: {str(e)}",
                extra={"params": params}
            )
            raise ValidationError(f"Invalid PolicyListSF parameters: {str(e)}")

    def validate_policy_detail_request(self, params: Dict[str, Any]) -> PolicyDetailSFRequest:
        """
        Validate PolicyDetailSF request parameters.
        
        Args:
            params: Request parameters dictionary
            
        Returns:
            Validated PolicyDetailSFRequest object
            
        Raises:
            ValidationError: If validation fails
        """
        try:
            # Create and validate the request model
            request = PolicyDetailSFRequest(**params)
            
            logger.info(
                "PolicyDetailSF request validated successfully",
                extra={"member_code": request.MEMBER_CODE}
            )
            
            return request
            
        except Exception as e:
            logger.error(
                f"PolicyDetailSF request validation failed: {str(e)}",
                extra={"params": params}
            )
            raise ValidationError(f"Invalid PolicyDetailSF parameters: {str(e)}")

    def validate_claim_list_request(self, params: Dict[str, Any]) -> ClaimListSFRequest:
        """
        Validate ClaimListSF request parameters.
        
        Args:
            params: Request parameters dictionary
            
        Returns:
            Validated ClaimListSFRequest object
            
        Raises:
            ValidationError: If validation fails
        """
        try:
            # Create and validate the request model
            request = ClaimListSFRequest(**params)
            
            logger.info(
                "ClaimListSF request validated successfully",
                extra={
                    "has_member_code": request.MEMBER_CODE is not None,
                    "has_insurer_code": request.INSURER_CODE is not None,
                    "has_citizen_id": request.CITIZEN_ID is not None,
                }
            )
            
            return request
            
        except Exception as e:
            logger.error(
                f"ClaimListSF request validation failed: {str(e)}",
                extra={"params": params}
            )
            raise ValidationError(f"Invalid ClaimListSF parameters: {str(e)}")

    def get_policy_list_search_strategy(self, request: PolicyListSFRequest) -> Tuple[str, Dict[str, Any]]:
        """
        Determine the search strategy for PolicyListSF based on parameters.
        
        Args:
            request: Validated PolicyListSF request
            
        Returns:
            Tuple of (strategy_name, search_params)
        """
        # Strategy 1: INSURER_CODE + CITIZEN_ID
        if request.CITIZEN_ID:
            return ("citizen_id", {
                "insurer_code": request.INSURER_CODE,
                "citizen_id": request.CITIZEN_ID
            })
        
        # Strategy 2-3: INSURER_CODE + POLICY_NO + NAME (TH/EN)
        if request.POLICY_NO and (request.NAME_TH or request.NAME_EN):
            return ("policy_no_name", {
                "insurer_code": request.INSURER_CODE,
                "policy_no": request.POLICY_NO,
                "name_th": request.NAME_TH,
                "name_en": request.NAME_EN
            })
        
        # Strategy 4-5: INSURER_CODE + CERTIFICATE_NO + NAME (TH/EN)
        if request.CERTIFICATE_NO and (request.NAME_TH or request.NAME_EN):
            return ("certificate_no_name", {
                "insurer_code": request.INSURER_CODE,
                "certificate_no": request.CERTIFICATE_NO,
                "name_th": request.NAME_TH,
                "name_en": request.NAME_EN
            })
        
        # Strategy 6-7: INSURER_CODE + STAFF_NO + NAME (TH/EN)
        if request.STAFF_NO and (request.NAME_TH or request.NAME_EN):
            return ("staff_no_name", {
                "insurer_code": request.INSURER_CODE,
                "staff_no": request.STAFF_NO,
                "name_th": request.NAME_TH,
                "name_en": request.NAME_EN
            })
        
        # Strategy 8: INSURER_CODE + OTHER_ID
        if request.OTHER_ID:
            return ("other_id", {
                "insurer_code": request.INSURER_CODE,
                "other_id": request.OTHER_ID
            })
        
        # Strategy 9-10: INSURER_CODE + NAME (TH/EN)
        if request.NAME_TH or request.NAME_EN:
            return ("name_only", {
                "insurer_code": request.INSURER_CODE,
                "name_th": request.NAME_TH,
                "name_en": request.NAME_EN
            })
        
        # This should never happen due to model validation
        raise ValidationError("Unable to determine search strategy")

    def get_claim_list_search_strategy(self, request: ClaimListSFRequest) -> Tuple[str, Dict[str, Any]]:
        """
        Determine the search strategy for ClaimListSF based on parameters.
        
        Args:
            request: Validated ClaimListSF request
            
        Returns:
            Tuple of (strategy_name, search_params)
        """
        # Strategy 1: MEMBER_CODE
        if request.MEMBER_CODE:
            return ("member_code", {
                "member_code": request.MEMBER_CODE
            })
        
        # Strategy 2: INSURER_CODE + CITIZEN_ID
        if request.INSURER_CODE and request.CITIZEN_ID:
            return ("insurer_citizen", {
                "insurer_code": request.INSURER_CODE,
                "citizen_id": request.CITIZEN_ID
            })
        
        # This should never happen due to model validation
        raise ValidationError("Unable to determine search strategy")


# Global validation service instance
_validation_service = None


def get_validation_service() -> ValidationService:
    """Get the global validation service instance."""
    global _validation_service
    if _validation_service is None:
        _validation_service = ValidationService()
    return _validation_service
