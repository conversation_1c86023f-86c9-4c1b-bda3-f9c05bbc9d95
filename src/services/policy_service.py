"""
Business logic service for policy-related operations.
Handles PolicyListSF and PolicyDetailSF endpoint logic.
"""

from typing import List, Dict, Any, Optional, Tuple
from src.data.policy_data import get_policy_manager
from src.data.claim_data import get_claim_manager
from src.models.request_models import PolicyListSFRequest, PolicyDetailSFRequest
from src.models.response_models import (
    PolicyListResponse,
    PolicyDetailSFResponse,
    PolicyDetailResponse,
    BenefitDetailResponse,
    ContractConditionResponse,
    MemberConditionResponse,
    ClaimHistoryResponse,
    PaymentDetailResponse,
)
from src.services.validation_service import get_validation_service
from src.utils.helpers import exact_match
from src.utils.exceptions import NotFoundError, BusinessLogicError, ValidationError
from src.utils.logger import get_logger

logger = get_logger(__name__)


class PolicyService:
    """Service for policy-related business logic."""

    def __init__(self):
        """Initialize the policy service."""
        self.policy_manager = get_policy_manager()
        self.claim_manager = get_claim_manager()
        self.validation_service = get_validation_service()

    def get_policy_list(self, params: Dict[str, Any]) -> List[PolicyListResponse]:
        """
        Get policy list based on search parameters.

        Args:
            params: Request parameters dictionary

        Returns:
            List of PolicyListResponse objects

        Raises:
            ValidationError: If parameters are invalid
            BusinessLogicError: If business logic fails
        """
        try:
            # Validate request parameters
            request = self.validation_service.validate_policy_list_request(params)

            # Get search strategy
            strategy, search_params = (
                self.validation_service.get_policy_list_search_strategy(request)
            )

            logger.info(
                f"Executing PolicyListSF search with strategy: {strategy}",
                extra={"search_params": search_params},
            )

            # Execute search based on strategy
            policies = self._execute_policy_search(strategy, search_params)

            # Convert to response format
            response_list = []
            for policy in policies:
                try:
                    policy_response = self._convert_policy_to_response(policy)
                    response_list.append(policy_response)
                except Exception as e:
                    logger.warning(
                        f"Failed to convert policy to response: {str(e)}",
                        extra={"member_code": policy.get("MemberCode")},
                    )
                    continue

            logger.info(
                f"PolicyListSF search completed successfully",
                extra={"strategy": strategy, "results_count": len(response_list)},
            )

            return response_list

        except Exception as e:
            logger.error(f"PolicyListSF search failed: {str(e)}")
            if isinstance(e, (ValidationError, NotFoundError, BusinessLogicError)):
                raise
            raise BusinessLogicError(f"Policy search failed: {str(e)}")

    def get_policy_detail(self, params: Dict[str, Any]) -> PolicyDetailSFResponse:
        """
        Get detailed policy information for a member.

        Args:
            params: Request parameters dictionary

        Returns:
            PolicyDetailSFResponse object

        Raises:
            ValidationError: If parameters are invalid
            NotFoundError: If member not found
            BusinessLogicError: If business logic fails
        """
        try:
            # Validate request parameters
            request = self.validation_service.validate_policy_detail_request(params)

            logger.info(f"Getting policy detail for member: {request.MEMBER_CODE}")

            # Get policy by member code
            policy = self.policy_manager.get_policy_by_member_code(request.MEMBER_CODE)
            if not policy:
                raise NotFoundError(f"Member not found: {request.MEMBER_CODE}")

            # Get all related data
            policy_details = self.policy_manager.get_policy_detail_by_member_code(
                request.MEMBER_CODE
            )
            benefits = self.policy_manager.get_benefits_by_member_code(
                request.MEMBER_CODE
            )
            conditions = self.claim_manager.get_all_conditions_by_member_code(
                request.MEMBER_CODE
            )
            claims = self.claim_manager.get_claims_by_member_code(request.MEMBER_CODE)

            # Convert to response format
            response = PolicyDetailSFResponse(
                ListPolicyDetail=[
                    self._convert_policy_detail_to_response(policy_details)
                ]
                if policy_details
                else [],
                BenefitList=[
                    self._convert_benefit_to_response(benefit) for benefit in benefits
                ],
                ListContractCondition=[
                    self._convert_contract_condition_to_response(condition)
                    for condition in conditions.get("contract_conditions", [])
                ],
                ListMemberCondition=[
                    self._convert_member_condition_to_response(condition)
                    for condition in conditions.get("member_conditions", [])
                ],
                ListClaimHistory=[
                    self._convert_claim_to_response(claim) for claim in claims
                ],
            )

            logger.info(
                f"Policy detail retrieved successfully for member: {request.MEMBER_CODE}",
                extra={
                    "policy_details_count": len(response.ListPolicyDetail),
                    "benefits_count": len(response.BenefitList),
                    "contract_conditions_count": len(response.ListContractCondition),
                    "member_conditions_count": len(response.ListMemberCondition),
                    "claims_count": len(response.ListClaimHistory),
                },
            )

            return response

        except Exception as e:
            logger.error(f"PolicyDetailSF failed: {str(e)}")
            if isinstance(e, (ValidationError, NotFoundError, BusinessLogicError)):
                raise
            raise BusinessLogicError(f"Policy detail retrieval failed: {str(e)}")

    def _execute_policy_search(
        self, strategy: str, search_params: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Execute policy search based on strategy."""
        insurer_code = search_params["insurer_code"]

        # Get active policies for the insurer
        policies = self.policy_manager.get_active_policies_by_insurer_code(insurer_code)

        if strategy == "citizen_id":
            # Filter by citizen ID
            citizen_id = search_params["citizen_id"]
            return [p for p in policies if p.get("CitizenID") == citizen_id]

        elif strategy == "policy_no_name":
            # Filter by policy number and name
            policy_no = search_params["policy_no"]
            name_th = search_params.get("name_th")
            name_en = search_params.get("name_en")

            filtered = [p for p in policies if p.get("PolicyNo") == policy_no]

            if name_th:
                filtered = [
                    p for p in filtered if exact_match(p.get("NameTH", ""), name_th)
                ]
            if name_en:
                filtered = [
                    p for p in filtered if exact_match(p.get("NameEN", ""), name_en)
                ]

            return filtered

        elif strategy == "certificate_no_name":
            # Filter by certificate number and name
            certificate_no = search_params["certificate_no"]
            name_th = search_params.get("name_th")
            name_en = search_params.get("name_en")

            filtered = [p for p in policies if p.get("CertificateNo") == certificate_no]

            if name_th:
                filtered = [
                    p for p in filtered if exact_match(p.get("NameTH", ""), name_th)
                ]
            if name_en:
                filtered = [
                    p for p in filtered if exact_match(p.get("NameEN", ""), name_en)
                ]

            return filtered

        elif strategy == "staff_no_name":
            # Filter by staff number and name
            staff_no = search_params["staff_no"]
            name_th = search_params.get("name_th")
            name_en = search_params.get("name_en")

            filtered = [p for p in policies if p.get("StaffNo") == staff_no]

            if name_th:
                filtered = [
                    p for p in filtered if exact_match(p.get("NameTH", ""), name_th)
                ]
            if name_en:
                filtered = [
                    p for p in filtered if exact_match(p.get("NameEN", ""), name_en)
                ]

            return filtered

        elif strategy == "other_id":
            # Filter by other ID
            other_id = search_params["other_id"]
            return [p for p in policies if p.get("OtherID") == other_id]

        elif strategy == "name_only":
            # Filter by name only
            name_th = search_params.get("name_th")
            name_en = search_params.get("name_en")

            if name_th:
                return [
                    p for p in policies if exact_match(p.get("NameTH", ""), name_th)
                ]
            if name_en:
                return [
                    p for p in policies if exact_match(p.get("NameEN", ""), name_en)
                ]

        return []

    def _convert_policy_to_response(self, policy: Dict[str, Any]) -> PolicyListResponse:
        """Convert policy data to PolicyListResponse."""
        # Get payment details for this member
        payment_details = self.policy_manager.get_payment_details_by_member_code(
            policy.get("MemberCode", "")
        )

        # Convert payment details to response format
        payment_responses = [
            PaymentDetailResponse(
                PaymentMethod=detail.get("PaymentMethod", ""),
                PayeeName=detail.get("PayeeName", ""),
                BankAccNo=detail.get("BankAccNo", ""),
                BankName=detail.get("BankName", ""),
                Primary=detail.get("Primary", ""),
            )
            for detail in payment_details
        ]

        return PolicyListResponse(
            MemberCode=policy.get("MemberCode", ""),
            MemberStatus=policy.get("MemberStatus", ""),
            TitleTH=policy.get("TitleTH", ""),
            NameTH=policy.get("NameTH", ""),
            SurnameTH=policy.get("SurnameTH", ""),
            TitleEN=policy.get("TitleEN", ""),
            NameEN=policy.get("NameEN", ""),
            SurnameEN=policy.get("SurnameEN", ""),
            CitizenID=policy.get("CitizenID", ""),
            OtherID=policy.get("OtherID", ""),
            StaffNo=policy.get("StaffNo", ""),
            InsurerCardNo=policy.get("InsurerCardNo", ""),
            InsPreviousCardNo=policy.get("InsPreviousCardNo", ""),
            PolicyNo=policy.get("PolicyNo", ""),
            CertificateNo=policy.get("CertificateNo", ""),
            MemberType=policy.get("MemberType", ""),
            PrincipleMemberCode=policy.get("PrincipleMemberCode", ""),
            PrincipleName=policy.get("PrincipleName", ""),
            VIP=policy.get("VIP", ""),
            VIPRemarks=policy.get("VIPRemarks", ""),
            CardType=policy.get("CardType", ""),
            Language=policy.get("Language", ""),
            InsurerCode=policy.get("InsurerCode", ""),
            InsurerName=policy.get("InsurerName", ""),
            InsurerNameEN=policy.get("InsurerNameEN", ""),
            CompanyCode=policy.get("CompanyCode", ""),
            CompanyName=policy.get("CompanyName", ""),
            CompanyNameEN=policy.get("CompanyNameEN", ""),
            BirthDate=policy.get("BirthDate", ""),
            Gender=policy.get("Gender", ""),
            Citizenship=policy.get("Citizenship", ""),
            CountryCode=policy.get("CountryCode", ""),
            PlanCode=policy.get("PlanCode", ""),
            PlanName=policy.get("PlanName", ""),
            PlanEffFrom=policy.get("PlanEffFrom", ""),
            PlanEffTo=policy.get("PlanEffTo", ""),
            Mobile=policy.get("Mobile", ""),
            Email=policy.get("Email", ""),
            PaymentDetail=payment_responses,
        )

    def _convert_policy_detail_to_response(
        self, policy_detail: Dict[str, Any]
    ) -> PolicyDetailResponse:
        """Convert policy detail data to PolicyDetailResponse."""
        return PolicyDetailResponse(
            MainBenefitCode=policy_detail.get("MainBenefitCode", ""),
            MainBenefit=policy_detail.get("MainBenefit", ""),
            MainBenefitEN=policy_detail.get("MainBenefitEN", ""),
            MainPlanLimitDesc=policy_detail.get("MainPlanLimitDesc", ""),
            MainPlanAmount=policy_detail.get("MainPlanAmount", ""),
            MainPlanUnit1=policy_detail.get("MainPlanUnit1", ""),
            MainPlanUnit2=policy_detail.get("MainPlanUnit2", ""),
            MainPlanBalance=policy_detail.get("MainPlanBalance", ""),
        )

    def _convert_benefit_to_response(
        self, benefit: Dict[str, Any]
    ) -> BenefitDetailResponse:
        """Convert benefit data to BenefitDetailResponse."""
        return BenefitDetailResponse(
            BenefitCode=benefit.get("BenefitCode", ""),
            BenefitTH=benefit.get("BenefitTH", ""),
            BenefitEN=benefit.get("BenefitEN", ""),
            SubBenefitCode=benefit.get("SubBenefitCode", ""),
            SubBenefitTH=benefit.get("SubBenefitTH", ""),
            SubBenefitEN=benefit.get("SubBenefitEN", ""),
            LimitCode=benefit.get("LimitCode", ""),
            LimitAmt=benefit.get("LimitAmt", ""),
            LimitUnit=benefit.get("LimitUnit", ""),
            BalanceCode=benefit.get("BalanceCode", ""),
            BalanceLimitAmt=benefit.get("BalanceLimitAmt", ""),
            BalanceUnit=benefit.get("BalanceUnit", ""),
            ComSubLimitCode=benefit.get("ComSubLimitCode", ""),
            ComSubLimitAmt=benefit.get("ComSubLimitAmt", ""),
            ComSubLimitUnit=benefit.get("ComSubLimitUnit", ""),
            BalComSubLimitCode=benefit.get("BalComSubLimitCode", ""),
            BalComSubLimitAmt=benefit.get("BalComSubLimitAmt", ""),
            BalComSubLimitUnit=benefit.get("BalComSubLimitUnit", ""),
            ComLimitCode=benefit.get("ComLimitCode", ""),
            ComLimitAmt=benefit.get("ComLimitAmt", ""),
            ComLimitUnit=benefit.get("ComLimitUnit", ""),
            BalComLimitCode=benefit.get("BalComLimitCode", ""),
            BalComLimitAmt=benefit.get("BalComLimitAmt", ""),
            BalComLimitUnit=benefit.get("BalComLimitUnit", ""),
        )

    def _convert_contract_condition_to_response(
        self, condition: Dict[str, Any]
    ) -> ContractConditionResponse:
        """Convert contract condition data to ContractConditionResponse."""
        return ContractConditionResponse(
            ConditionType=condition.get("ConditionType", ""),
            ConditionApply=condition.get("ConditionApply", ""),
            EffFromDate=condition.get("EffFromDate", ""),
            EffToDate=condition.get("EffToDate", ""),
            ConditionDetail=condition.get("ConditionDetail", ""),
            Action=condition.get("Action", ""),
            SourceCondition=condition.get("SourceCondition", ""),
            Remarks=condition.get("Remarks", ""),
            CreateBy=condition.get("CreateBy", ""),
            CreateDateTime=condition.get("CreateDateTime", ""),
            ModifiedBy=condition.get("ModifiedBy", ""),
            ModifiedDateTime=condition.get("ModifiedDateTime", ""),
        )

    def _convert_member_condition_to_response(
        self, condition: Dict[str, Any]
    ) -> MemberConditionResponse:
        """Convert member condition data to MemberConditionResponse."""
        return MemberConditionResponse(
            ConditionType=condition.get("ConditionType", ""),
            EffFromDate=condition.get("EffFromDate", ""),
            EffToDate=condition.get("EffToDate", ""),
            ConditionDetail=condition.get("ConditionDetail", ""),
            Action=condition.get("Action", ""),
            SourceCondition=condition.get("SourceCondition", ""),
            Remarks=condition.get("Remarks", ""),
            CreateBy=condition.get("CreateBy", ""),
            CreateDateTime=condition.get("CreateDateTime", ""),
            ModifiedBy=condition.get("ModifiedBy", ""),
            ModifiedDateTime=condition.get("ModifiedDateTime", ""),
        )

    def _convert_claim_to_response(self, claim: Dict[str, Any]) -> ClaimHistoryResponse:
        """Convert claim data to ClaimHistoryResponse."""
        return ClaimHistoryResponse(
            ClaimNo=claim.get("ClaimNo", ""),
            MainBenefit=claim.get("MainBenefit", ""),
            ClaimSource=claim.get("ClaimSource", ""),
            ClaimType=claim.get("ClaimType", ""),
            PatientNameTH=claim.get("PatientNameTH", ""),
            PatientNameEN=claim.get("PatientNameEN", ""),
            ClaimStatus=claim.get("ClaimStatus", ""),
            CitizenID=claim.get("CitizenID", ""),
            VisitDate=claim.get("VisitDate", ""),
            DischargeDate=claim.get("DischargeDate", ""),
            AccidentDate=claim.get("AccidentDate", ""),
            IncurredAmt=claim.get("IncurredAmt", ""),
            PayableAmt=claim.get("PayableAmt", ""),
            ContractNo=claim.get("ContractNo", ""),
            InsurerCode=claim.get("InsurerCode", ""),
            InsurerTH=claim.get("InsurerTH", ""),
            InsurerEN=claim.get("InsurerEN", ""),
            CompanyCode=claim.get("CompanyCode", ""),
            CompanyTH=claim.get("CompanyTH", ""),
            CompanyEN=claim.get("CompanyEN", ""),
            CardType=claim.get("CardType", ""),
            PolicyNo=claim.get("PolicyNo", ""),
            ProviderCode=claim.get("ProviderCode", ""),
            ProviderTH=claim.get("ProviderTH", ""),
            ProviderEN=claim.get("ProviderEN", ""),
            BatchNo=claim.get("BatchNo", ""),
            DiagCode=claim.get("DiagCode", ""),
            DiagTH=claim.get("DiagTH", ""),
            DiagEN=claim.get("DiagEN", ""),
            PaymentDate=claim.get("PaymentDate", ""),
            MemberCode=claim.get("MemberCode", ""),
        )


# Global policy service instance
_policy_service = None


def get_policy_service() -> PolicyService:
    """Get the global policy service instance."""
    global _policy_service
    if _policy_service is None:
        _policy_service = PolicyService()
    return _policy_service
