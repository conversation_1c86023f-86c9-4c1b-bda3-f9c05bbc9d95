"""
Shared dependencies and validation for TPA API endpoints.
Provides common dependencies for FastAPI routes.
"""

from typing import Dict, Any
from fastapi import Request, HTTPException
from src.services.policy_service import get_policy_service
from src.services.claim_service import get_claim_service
from src.services.validation_service import get_validation_service
from src.utils.exceptions import (
    ValidationError,
    NotFoundError,
    BusinessLogicError,
    DataLoadingError,
    MemberNotFoundError,
    TPAAPIException,
    EXCEPTION_HANDLERS,
)
from src.utils.logger import get_logger

logger = get_logger(__name__)


def get_query_params(request: Request) -> Dict[str, Any]:
    """
    Extract and normalize query parameters from request.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Dictionary of query parameters with None values for missing params
    """
    params = {}
    
    # Get all query parameters
    for key, value in request.query_params.items():
        # Convert empty strings to None
        params[key] = value if value.strip() else None
    
    logger.debug(
        "Extracted query parameters",
        extra={"params": params, "endpoint": request.url.path}
    )
    
    return params


def handle_service_exception(error: Exception) -> HTTPException:
    """
    Convert service exceptions to appropriate HTTP exceptions.
    
    Args:
        error: Exception from service layer
        
    Returns:
        HTTPException with appropriate status code and details
    """
    # Check if we have a specific handler for this exception type
    for exception_type, handler in EXCEPTION_HANDLERS.items():
        if isinstance(error, exception_type):
            logger.info(
                f"Converting {exception_type.__name__} to HTTP response",
                extra={"error_message": str(error)}
            )
            return handler(error)
    
    # Fallback for unexpected exceptions
    logger.error(
        f"Unexpected exception in service layer: {str(error)}",
        extra={"exception_type": type(error).__name__}
    )
    
    return HTTPException(
        status_code=500,
        detail={
            "error": "Internal Server Error",
            "message": "An unexpected error occurred",
            "details": {}
        }
    )


def get_policy_service_dependency():
    """Dependency to get policy service instance."""
    return get_policy_service()


def get_claim_service_dependency():
    """Dependency to get claim service instance."""
    return get_claim_service()


def get_validation_service_dependency():
    """Dependency to get validation service instance."""
    return get_validation_service()
