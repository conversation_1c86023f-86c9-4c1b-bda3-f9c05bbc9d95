"""
ClaimListSF endpoint implementation.
Handles claim list retrieval with multiple parameter combinations.
"""

from typing import List
from fastapi import APIRouter, Request, Depends, HTTPException
from src.models.response_models import ClaimHistoryResponse
from src.services.claim_service import ClaimService
from src.api.dependencies import (
    get_query_params,
    handle_service_exception,
    get_claim_service_dependency,
)
from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()


@router.get("/ClaimListSF", response_model=List[ClaimHistoryResponse])
async def claim_list_sf(
    request: Request,
    claim_service: ClaimService = Depends(get_claim_service_dependency),
) -> List[ClaimHistoryResponse]:
    """
    Get claim list based on search parameters.
    
    Supports 2 different parameter combinations:
    1. MEMBER_CODE - Direct member code lookup
    2. INSURER_CODE + CITIZEN_ID - Cross-reference lookup via policy
    
    Returns claims with valid statuses only:
    - Approved
    - Authorized
    - Open
    - Paid
    - Pending
    - Pending For Approval
    - Rejected
    
    Args:
        request: FastAPI request object
        claim_service: Claim service dependency
        
    Returns:
        List of ClaimHistoryResponse objects
        
    Raises:
        HTTPException: For validation errors, not found, or business logic errors
    """
    try:
        # Extract query parameters
        params = get_query_params(request)
        
        logger.info(
            "ClaimListSF endpoint called",
            extra={
                "params": params,
                "client_ip": request.client.host if request.client else "unknown",
                "user_agent": request.headers.get("user-agent", "unknown"),
            }
        )
        
        # Call service layer
        result = claim_service.get_claim_list(params)
        
        logger.info(
            "ClaimListSF request completed successfully",
            extra={
                "results_count": len(result),
                "params": params,
            }
        )
        
        return result
        
    except Exception as e:
        logger.error(
            f"ClaimListSF endpoint error: {str(e)}",
            extra={
                "params": params if 'params' in locals() else {},
                "exception_type": type(e).__name__,
            }
        )
        
        # Convert service exceptions to HTTP exceptions
        raise handle_service_exception(e)
