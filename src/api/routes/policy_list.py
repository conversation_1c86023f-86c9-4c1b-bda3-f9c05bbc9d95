"""
PolicyListSF endpoint implementation.
Handles policy list retrieval with multiple parameter combinations.
"""

from typing import List
from fastapi import APIRouter, Request, Depends, HTTPException
from src.models.response_models import PolicyListResponse
from src.services.policy_service import PolicyService
from src.api.dependencies import (
    get_query_params,
    handle_service_exception,
    get_policy_service_dependency,
)
from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()


@router.get("/PolicyListSF", response_model=List[PolicyListResponse])
async def policy_list_sf(
    request: Request,
    policy_service: PolicyService = Depends(get_policy_service_dependency),
) -> List[PolicyListResponse]:
    """
    Get policy list based on search parameters.
    
    Supports 10 different parameter combinations:
    1. INSURER_CODE + CITIZEN_ID
    2. INSURER_CODE + POLICY_NO + NAME_TH
    3. INSURER_CODE + POLICY_NO + NAME_EN
    4. INSURER_CODE + CERTIFICATE_NO + NAME_TH
    5. INSURER_CODE + CERTIFICATE_NO + NAME_EN
    6. INSURER_CODE + STAFF_NO + NAME_TH
    7. INSURER_CODE + STAFF_NO + NAME_EN
    8. INSURER_CODE + OTHER_ID
    9. INSURER_CODE + NAME_TH
    10. INSURER_CODE + NAME_EN
    
    Args:
        request: FastAPI request object
        policy_service: Policy service dependency
        
    Returns:
        List of PolicyListResponse objects
        
    Raises:
        HTTPException: For validation errors, not found, or business logic errors
    """
    try:
        # Extract query parameters
        params = get_query_params(request)
        
        logger.info(
            "PolicyListSF endpoint called",
            extra={
                "params": params,
                "client_ip": request.client.host if request.client else "unknown",
                "user_agent": request.headers.get("user-agent", "unknown"),
            }
        )
        
        # Call service layer
        result = policy_service.get_policy_list(params)
        
        logger.info(
            "PolicyListSF request completed successfully",
            extra={
                "results_count": len(result),
                "params": params,
            }
        )
        
        return result
        
    except Exception as e:
        logger.error(
            f"PolicyListSF endpoint error: {str(e)}",
            extra={
                "params": params if 'params' in locals() else {},
                "exception_type": type(e).__name__,
            }
        )
        
        # Convert service exceptions to HTTP exceptions
        raise handle_service_exception(e)
