"""
PolicyDetailSF endpoint implementation.
Handles detailed policy information retrieval for a specific member.
"""

from fastapi import APIRouter, Request, Depends, HTTPException
from src.models.response_models import PolicyDetailSFResponse
from src.services.policy_service import PolicyService
from src.api.dependencies import (
    get_query_params,
    handle_service_exception,
    get_policy_service_dependency,
)
from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()


@router.get("/PolicyDetailSF", response_model=PolicyDetailSFResponse)
async def policy_detail_sf(
    request: Request,
    policy_service: PolicyService = Depends(get_policy_service_dependency),
) -> PolicyDetailSFResponse:
    """
    Get detailed policy information for a specific member.
    
    Required Parameters:
    - MEMBER_CODE: Member code to retrieve policy details for
    
    Returns comprehensive policy information including:
    - Policy details
    - Benefit information
    - Contract conditions
    - Member conditions
    - Claim history
    
    Args:
        request: FastAPI request object
        policy_service: Policy service dependency
        
    Returns:
        PolicyDetailSFResponse object with complete member information
        
    Raises:
        HTTPException: For validation errors, member not found, or business logic errors
    """
    try:
        # Extract query parameters
        params = get_query_params(request)
        
        logger.info(
            "PolicyDetailSF endpoint called",
            extra={
                "params": params,
                "client_ip": request.client.host if request.client else "unknown",
                "user_agent": request.headers.get("user-agent", "unknown"),
            }
        )
        
        # Call service layer
        result = policy_service.get_policy_detail(params)
        
        logger.info(
            "PolicyDetailSF request completed successfully",
            extra={
                "member_code": params.get("MEMBER_CODE"),
                "policy_details_count": len(result.ListPolicyDetail),
                "benefits_count": len(result.BenefitList),
                "contract_conditions_count": len(result.ListContractCondition),
                "member_conditions_count": len(result.ListMemberCondition),
                "claims_count": len(result.ListClaimHistory),
            }
        )
        
        return result
        
    except Exception as e:
        logger.error(
            f"PolicyDetailSF endpoint error: {str(e)}",
            extra={
                "params": params if 'params' in locals() else {},
                "exception_type": type(e).__name__,
            }
        )
        
        # Convert service exceptions to HTTP exceptions
        raise handle_service_exception(e)
