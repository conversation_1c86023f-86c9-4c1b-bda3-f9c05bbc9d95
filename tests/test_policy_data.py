"""
Unit tests for policy data management functionality.
"""

import pytest
from unittest.mock import patch, MagicMock
from datetime import date

from src.data.policy_data import PolicyDataManager, get_policy_manager


class TestPolicyDataManager:
    """Test cases for PolicyDataManager class."""

    def setup_method(self):
        """Set up test data for each test method."""
        self.sample_policies = [
            {
                "MemberCode": "MEM001",
                "CitizenID": "**********123",
                "InsurerCode": "INS001",
                "CompanyCode": "COM001",
                "PolicyNo": "POL001",
                "NameTH": "สมชาย",
                "NameEN": "Somchai",
                "StaffNo": "ST001",
                "InsurerCardNo": "IC001",
                "PlanEffFrom": "2024-01-01",
                "PlanEffTo": "2024-12-31",
            },
            {
                "MemberCode": "MEM002",
                "CitizenID": "**********124",
                "InsurerCode": "INS001",
                "CompanyCode": "COM001",
                "PolicyNo": "POL001",
                "NameTH": "สุดา",
                "NameEN": "Suda",
                "StaffNo": "ST002",
                "InsurerCardNo": "IC002",
                "PlanEffFrom": "2024-01-01",
                "PlanEffTo": "2024-12-31",
            },
            {
                "MemberCode": "MEM003",
                "CitizenID": "**********125",
                "InsurerCode": "INS002",
                "CompanyCode": "COM002",
                "PolicyNo": "POL002",
                "NameTH": "มาลี",
                "NameEN": "Malee",
                "StaffNo": "ST003",
                "InsurerCardNo": "IC003",
                "PlanEffFrom": "2023-01-01",
                "PlanEffTo": "2023-12-31",  # Expired policy
            },
        ]

        self.sample_payment_details = [
            {
                "MemberCode": "MEM001",
                "PaymentMethod": "Bank Transfer",
                "PayeeName": "สมชาย ใจดี",
                "BankAccNo": "**********",
                "BankName": "ธนาคารกรุงเทพ",
                "Primary": "Y",
            }
        ]

        self.sample_policy_details = [
            {
                "MemberCode": "MEM001",
                "MainBenefitCode": "MB001",
                "MainBenefit": "ประกันสุขภาพ",
                "MainBenefitEN": "Health Insurance",
                "MainPlanLimitDesc": "ค่ารักษาพยาบาล",
                "MainPlanAmount": "1000000",
                "MainPlanUnit1": "บาท",
                "MainPlanUnit2": "ต่อปี",
                "MainPlanBalance": "800000",
            }
        ]

        self.sample_benefits = [
            {
                "MemberCode": "MEM001",
                "BenefitCode": "BEN001",
                "BenefitTH": "ค่ารักษาพยาบาล",
                "BenefitEN": "Medical Treatment",
                "SubBenefitCode": "SUB001",
                "SubBenefitTH": "ค่าห้องพัก",
                "SubBenefitEN": "Room Charge",
                "LimitCode": "LIM001",
                "LimitAmt": "5000",
                "LimitUnit": "บาท/วัน",
            }
        ]

    @patch("src.data.policy_data.get_data_loader")
    def test_init(self, mock_get_loader):
        """Test PolicyDataManager initialization."""
        manager = PolicyDataManager()

        assert manager.data_loader is not None
        assert manager._indexed is False
        assert len(manager._policy_by_member_code) == 0

    @patch("src.data.policy_data.get_data_loader")
    @patch("src.data.policy_data.is_policy_active")
    def test_build_indexes(self, mock_is_active, mock_get_loader):
        """Test index building functionality."""
        # Mock data loader
        mock_loader = MagicMock()

        # Create proper mock objects that return the correct data
        mock_policies = []
        for p in self.sample_policies:
            mock_policy = MagicMock()
            mock_policy.model_dump.return_value = p
            mock_policies.append(mock_policy)

        mock_payment_details = []
        for p in self.sample_payment_details:
            mock_payment = MagicMock()
            mock_payment.model_dump.return_value = p
            mock_payment_details.append(mock_payment)

        mock_policy_details = []
        for p in self.sample_policy_details:
            mock_detail = MagicMock()
            mock_detail.model_dump.return_value = p
            mock_policy_details.append(mock_detail)

        mock_benefits = []
        for b in self.sample_benefits:
            mock_benefit = MagicMock()
            mock_benefit.model_dump.return_value = b
            mock_benefits.append(mock_benefit)

        mock_loader.get_policies.return_value = mock_policies
        mock_loader.get_payment_details.return_value = mock_payment_details
        mock_loader.get_policy_details.return_value = mock_policy_details
        mock_loader.get_benefits.return_value = mock_benefits
        mock_get_loader.return_value = mock_loader

        manager = PolicyDataManager()
        manager._build_indexes()

        assert manager._indexed is True
        assert len(manager._policy_by_member_code) == 3
        assert "MEM001" in manager._policy_by_member_code
        assert "MEM002" in manager._policy_by_member_code
        assert "MEM003" in manager._policy_by_member_code

    @patch("src.data.policy_data.get_data_loader")
    def test_get_policy_by_member_code(self, mock_get_loader):
        """Test getting policy by member code."""
        mock_loader = MagicMock()
        mock_policy = MagicMock()
        mock_policy.model_dump.return_value = self.sample_policies[0]
        mock_loader.get_policies.return_value = [mock_policy]
        mock_loader.get_payment_details.return_value = []
        mock_loader.get_policy_details.return_value = []
        mock_loader.get_benefits.return_value = []
        mock_get_loader.return_value = mock_loader

        manager = PolicyDataManager()
        policy = manager.get_policy_by_member_code("MEM001")

        assert policy is not None
        assert policy["MemberCode"] == "MEM001"
        assert policy["CitizenID"] == "**********123"

    @patch("src.data.policy_data.get_data_loader")
    def test_get_policy_by_citizen_id(self, mock_get_loader):
        """Test getting policy by citizen ID."""
        mock_loader = MagicMock()
        mock_policy = MagicMock()
        mock_policy.model_dump.return_value = self.sample_policies[0]
        mock_loader.get_policies.return_value = [mock_policy]
        mock_loader.get_payment_details.return_value = []
        mock_loader.get_policy_details.return_value = []
        mock_loader.get_benefits.return_value = []
        mock_get_loader.return_value = mock_loader

        manager = PolicyDataManager()
        policy = manager.get_policy_by_citizen_id("**********123")

        assert policy is not None
        assert policy["MemberCode"] == "MEM001"
        assert policy["CitizenID"] == "**********123"

    @patch("src.data.policy_data.get_data_loader")
    @patch("src.data.policy_data.is_policy_active")
    def test_get_active_policies_by_insurer_code(self, mock_is_active, mock_get_loader):
        """Test getting active policies by insurer code."""
        # Mock policy active status
        mock_is_active.side_effect = lambda eff_from, eff_to: eff_from == "2024-01-01"

        mock_loader = MagicMock()

        # Create proper mock objects
        mock_policies = []
        for p in self.sample_policies:
            mock_policy = MagicMock()
            mock_policy.model_dump.return_value = p
            mock_policies.append(mock_policy)

        mock_loader.get_policies.return_value = mock_policies
        mock_loader.get_payment_details.return_value = []
        mock_loader.get_policy_details.return_value = []
        mock_loader.get_benefits.return_value = []
        mock_get_loader.return_value = mock_loader

        manager = PolicyDataManager()
        active_policies = manager.get_active_policies_by_insurer_code("INS001")

        # Should return 2 active policies (MEM001, MEM002) but not MEM003 (expired)
        assert len(active_policies) == 2
        member_codes = [p["MemberCode"] for p in active_policies]
        assert "MEM001" in member_codes
        assert "MEM002" in member_codes

    @patch("src.data.policy_data.get_data_loader")
    @patch("src.data.policy_data.is_policy_active")
    @patch("src.data.policy_data.exact_match")
    def test_filter_policies_by_criteria(
        self, mock_exact_match, mock_is_active, mock_get_loader
    ):
        """Test filtering policies by multiple criteria."""
        # Mock dependencies
        mock_is_active.return_value = True
        mock_exact_match.return_value = True

        mock_loader = MagicMock()

        # Create proper mock objects
        mock_policies = []
        for p in self.sample_policies:
            mock_policy = MagicMock()
            mock_policy.model_dump.return_value = p
            mock_policies.append(mock_policy)

        mock_loader.get_policies.return_value = mock_policies
        mock_loader.get_payment_details.return_value = []
        mock_loader.get_policy_details.return_value = []
        mock_loader.get_benefits.return_value = []
        mock_get_loader.return_value = mock_loader

        manager = PolicyDataManager()

        # Test filtering by company code
        policies = manager.filter_policies_by_criteria(
            insurer_code="INS001", company_code="COM001"
        )

        assert len(policies) == 2  # MEM001 and MEM002

        # Test filtering by name
        policies = manager.filter_policies_by_criteria(
            insurer_code="INS001", name_th="สมชาย"
        )

        # Should call exact_match for name comparison
        mock_exact_match.assert_called()

    @patch("src.data.policy_data.get_data_loader")
    def test_get_payment_details_by_member_code(self, mock_get_loader):
        """Test getting payment details by member code."""
        mock_loader = MagicMock()
        mock_loader.get_policies.return_value = []
        mock_payment = MagicMock()
        mock_payment.model_dump.return_value = self.sample_payment_details[0]
        mock_loader.get_payment_details.return_value = [mock_payment]
        mock_loader.get_policy_details.return_value = []
        mock_loader.get_benefits.return_value = []
        mock_get_loader.return_value = mock_loader

        manager = PolicyDataManager()
        payment_details = manager.get_payment_details_by_member_code("MEM001")

        assert len(payment_details) == 1
        assert payment_details[0]["MemberCode"] == "MEM001"
        assert payment_details[0]["PaymentMethod"] == "Bank Transfer"

    def test_get_policy_manager_singleton(self):
        """Test that get_policy_manager returns singleton instance."""
        manager1 = get_policy_manager()
        manager2 = get_policy_manager()

        assert manager1 is manager2
