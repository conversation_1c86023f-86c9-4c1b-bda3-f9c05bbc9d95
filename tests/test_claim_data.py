"""
Unit tests for claim data management functionality.
"""

import pytest
from unittest.mock import patch, MagicMock

from src.data.claim_data import ClaimDataManager, get_claim_manager


class TestClaimDataManager:
    """Test cases for ClaimDataManager class."""

    def setup_method(self):
        """Set up test data for each test method."""
        self.sample_claims = [
            {
                "ClaimNo": "CLM001",
                "MemberCode": "MEM001",
                "CitizenID": "1234567890123",
                "InsurerCode": "INS001",
                "ClaimStatus": "Approved",
                "MainBenefit": "ประกันสุขภาพ",
                "ClaimSource": "Hospital",
                "ClaimType": "IPD",
                "PatientNameTH": "สมชาย ใจดี",
                "PatientNameEN": "Somchai Jaidee",
                "VisitDate": "2024-01-15",
                "IncurredAmt": "50000",
                "PayableAmt": "45000",
            },
            {
                "ClaimNo": "CLM002",
                "MemberCode": "MEM001",
                "CitizenID": "1234567890123",
                "InsurerCode": "INS001",
                "ClaimStatus": "Paid",
                "MainBenefit": "ประกันสุขภาพ",
                "ClaimSource": "Clinic",
                "ClaimType": "OPD",
                "PatientNameTH": "สมชาย ใจดี",
                "PatientNameEN": "Somchai Jaidee",
                "VisitDate": "2024-02-10",
                "IncurredAmt": "2000",
                "PayableAmt": "1800",
            },
            {
                "ClaimNo": "CLM003",
                "MemberCode": "MEM002",
                "CitizenID": "1234567890124",
                "InsurerCode": "INS001",
                "ClaimStatus": "InvalidStatus",  # Invalid status
                "MainBenefit": "ประกันสุขภาพ",
                "ClaimSource": "Hospital",
                "ClaimType": "IPD",
                "PatientNameTH": "สุดา รักดี",
                "PatientNameEN": "Suda Rakdee",
                "VisitDate": "2024-01-20",
                "IncurredAmt": "30000",
                "PayableAmt": "25000",
            },
        ]

        self.sample_contract_conditions = [
            {
                "MemberCode": "MEM001",
                "ConditionType": "Exclusion",
                "ConditionApply": "Y",
                "EffFromDate": "2024-01-01",
                "EffToDate": "2024-12-31",
                "ConditionDetail": "Pre-existing condition exclusion",
                "Action": "Exclude",
                "SourceCondition": "Medical",
                "Remarks": "Diabetes exclusion",
                "CreateBy": "SYSTEM",
                "CreateDateTime": "2024-01-01 00:00:00",
            }
        ]

        self.sample_member_conditions = [
            {
                "MemberCode": "MEM001",
                "ConditionType": "Special",
                "EffFromDate": "2024-01-01",
                "EffToDate": "2024-12-31",
                "ConditionDetail": "VIP member benefits",
                "Action": "Include",
                "SourceCondition": "Membership",
                "Remarks": "VIP status",
                "CreateBy": "ADMIN",
                "CreateDateTime": "2024-01-01 00:00:00",
            }
        ]

    @patch("src.data.claim_data.get_data_loader")
    def test_init(self, mock_get_loader):
        """Test ClaimDataManager initialization."""
        manager = ClaimDataManager()

        assert manager.data_loader is not None
        assert manager._indexed is False
        assert len(manager._claims_by_member_code) == 0

    @patch("src.data.claim_data.get_data_loader")
    @patch("src.data.claim_data.validate_claim_status")
    def test_build_indexes(self, mock_validate_status, mock_get_loader):
        """Test index building functionality."""

        # Mock claim status validation
        def validate_side_effect(status):
            return status in [
                "Approved",
                "Paid",
                "Pending",
                "Rejected",
                "Open",
                "Authorized",
                "Pending For Approval",
            ]

        mock_validate_status.side_effect = validate_side_effect

        # Mock data loader
        mock_loader = MagicMock()

        # Create proper mock objects
        mock_claims = []
        for c in self.sample_claims:
            mock_claim = MagicMock()
            mock_claim.model_dump.return_value = c
            mock_claims.append(mock_claim)

        mock_contract_conditions = []
        for c in self.sample_contract_conditions:
            mock_condition = MagicMock()
            mock_condition.model_dump.return_value = c
            mock_contract_conditions.append(mock_condition)

        mock_member_conditions = []
        for c in self.sample_member_conditions:
            mock_condition = MagicMock()
            mock_condition.model_dump.return_value = c
            mock_member_conditions.append(mock_condition)

        mock_loader.get_claims.return_value = mock_claims
        mock_loader.get_contract_conditions.return_value = mock_contract_conditions
        mock_loader.get_member_conditions.return_value = mock_member_conditions
        mock_get_loader.return_value = mock_loader

        manager = ClaimDataManager()
        manager._build_indexes()

        assert manager._indexed is True
        # Should only index valid claims (CLM001 and CLM002, not CLM003 with invalid status)
        assert len(manager._claims_by_member_code) == 1  # Only MEM001 has valid claims
        assert "MEM001" in manager._claims_by_member_code
        assert len(manager._claims_by_member_code["MEM001"]) == 2

    @patch("src.data.claim_data.get_data_loader")
    @patch("src.data.claim_data.validate_claim_status")
    def test_get_claims_by_member_code(self, mock_validate_status, mock_get_loader):
        """Test getting claims by member code."""
        mock_validate_status.return_value = True

        mock_loader = MagicMock()
        mock_claim = MagicMock()
        mock_claim.model_dump.return_value = self.sample_claims[0]
        mock_loader.get_claims.return_value = [mock_claim]
        mock_loader.get_contract_conditions.return_value = []
        mock_loader.get_member_conditions.return_value = []
        mock_get_loader.return_value = mock_loader

        manager = ClaimDataManager()
        claims = manager.get_claims_by_member_code("MEM001")

        assert len(claims) == 1
        assert claims[0]["ClaimNo"] == "CLM001"
        assert claims[0]["MemberCode"] == "MEM001"

    @patch("src.data.claim_data.get_data_loader")
    @patch("src.data.claim_data.validate_claim_status")
    def test_get_claims_by_citizen_id(self, mock_validate_status, mock_get_loader):
        """Test getting claims by citizen ID."""
        mock_validate_status.return_value = True

        mock_loader = MagicMock()
        mock_claim = MagicMock()
        mock_claim.model_dump.return_value = self.sample_claims[0]
        mock_loader.get_claims.return_value = [mock_claim]
        mock_loader.get_contract_conditions.return_value = []
        mock_loader.get_member_conditions.return_value = []
        mock_get_loader.return_value = mock_loader

        manager = ClaimDataManager()
        claims = manager.get_claims_by_citizen_id("1234567890123")

        assert len(claims) == 1
        assert claims[0]["CitizenID"] == "1234567890123"

    @patch("src.data.claim_data.get_data_loader")
    @patch("src.data.claim_data.validate_claim_status")
    def test_get_claims_by_insurer_and_citizen(
        self, mock_validate_status, mock_get_loader
    ):
        """Test getting claims by insurer code and citizen ID combination."""
        mock_validate_status.return_value = True

        mock_loader = MagicMock()
        mock_claims = []
        for c in self.sample_claims[:2]:
            mock_claim = MagicMock()
            mock_claim.model_dump.return_value = c
            mock_claims.append(mock_claim)
        mock_loader.get_claims.return_value = mock_claims
        mock_loader.get_contract_conditions.return_value = []
        mock_loader.get_member_conditions.return_value = []
        mock_get_loader.return_value = mock_loader

        manager = ClaimDataManager()
        claims = manager.get_claims_by_insurer_and_citizen("INS001", "1234567890123")

        assert len(claims) == 2
        assert all(claim["InsurerCode"] == "INS001" for claim in claims)
        assert all(claim["CitizenID"] == "1234567890123" for claim in claims)

    @patch("src.data.claim_data.get_data_loader")
    def test_get_contract_conditions_by_member_code(self, mock_get_loader):
        """Test getting contract conditions by member code."""
        mock_loader = MagicMock()
        mock_loader.get_claims.return_value = []
        mock_condition = MagicMock()
        mock_condition.model_dump.return_value = self.sample_contract_conditions[0]
        mock_loader.get_contract_conditions.return_value = [mock_condition]
        mock_loader.get_member_conditions.return_value = []
        mock_get_loader.return_value = mock_loader

        manager = ClaimDataManager()
        conditions = manager.get_contract_conditions_by_member_code("MEM001")

        assert len(conditions) == 1
        assert conditions[0]["MemberCode"] == "MEM001"
        assert conditions[0]["ConditionType"] == "Exclusion"

    @patch("src.data.claim_data.get_data_loader")
    def test_get_member_conditions_by_member_code(self, mock_get_loader):
        """Test getting member conditions by member code."""
        mock_loader = MagicMock()
        mock_loader.get_claims.return_value = []
        mock_loader.get_contract_conditions.return_value = []
        mock_condition = MagicMock()
        mock_condition.model_dump.return_value = self.sample_member_conditions[0]
        mock_loader.get_member_conditions.return_value = [mock_condition]
        mock_get_loader.return_value = mock_loader

        manager = ClaimDataManager()
        conditions = manager.get_member_conditions_by_member_code("MEM001")

        assert len(conditions) == 1
        assert conditions[0]["MemberCode"] == "MEM001"
        assert conditions[0]["ConditionType"] == "Special"

    @patch("src.data.claim_data.get_data_loader")
    def test_get_all_conditions_by_member_code(self, mock_get_loader):
        """Test getting all conditions by member code."""
        mock_loader = MagicMock()
        mock_loader.get_claims.return_value = []
        mock_contract_condition = MagicMock()
        mock_contract_condition.model_dump.return_value = (
            self.sample_contract_conditions[0]
        )
        mock_loader.get_contract_conditions.return_value = [mock_contract_condition]

        mock_member_condition = MagicMock()
        mock_member_condition.model_dump.return_value = self.sample_member_conditions[0]
        mock_loader.get_member_conditions.return_value = [mock_member_condition]
        mock_get_loader.return_value = mock_loader

        manager = ClaimDataManager()
        all_conditions = manager.get_all_conditions_by_member_code("MEM001")

        assert "contract_conditions" in all_conditions
        assert "member_conditions" in all_conditions
        assert len(all_conditions["contract_conditions"]) == 1
        assert len(all_conditions["member_conditions"]) == 1

    def test_get_valid_claim_statuses(self):
        """Test getting valid claim statuses."""
        manager = ClaimDataManager()
        statuses = manager.get_valid_claim_statuses()

        expected_statuses = [
            "Approved",
            "Authorized",
            "Open",
            "Paid",
            "Pending",
            "Pending For Approval",
            "Rejected",
        ]

        assert len(statuses) == len(expected_statuses)
        for status in expected_statuses:
            assert status in statuses

    @patch("src.data.claim_data.validate_claim_status")
    def test_validate_claim_status(self, mock_validate):
        """Test claim status validation."""
        mock_validate.return_value = True

        manager = ClaimDataManager()
        result = manager.validate_claim_status("Approved")

        assert result is True
        mock_validate.assert_called_once_with("Approved")

    def test_get_claim_manager_singleton(self):
        """Test that get_claim_manager returns singleton instance."""
        manager1 = get_claim_manager()
        manager2 = get_claim_manager()

        assert manager1 is manager2
