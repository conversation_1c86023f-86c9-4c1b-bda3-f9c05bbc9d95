"""
Tests for Phase 4 business logic services.
Tests validation service, policy service, and claim service.
"""

import pytest
from unittest.mock import patch, MagicMock
from src.services.validation_service import get_validation_service
from src.services.policy_service import get_policy_service
from src.services.claim_service import get_claim_service
from src.utils.exceptions import ValidationError, NotFoundError
from src.data.mock_data import initialize_data


class TestValidationService:
    """Test validation service functionality."""

    def test_validate_policy_list_request_citizen_id(self):
        """Test PolicyListSF validation with citizen ID."""
        validation_service = get_validation_service()

        params = {"INSURER_CODE": "INS001", "CITIZEN_ID": "1234567890123"}

        request = validation_service.validate_policy_list_request(params)
        assert request.INSURER_CODE == "INS001"
        assert request.CITIZEN_ID == "1234567890123"

    def test_validate_policy_list_request_invalid(self):
        """Test PolicyListSF validation with invalid parameters."""
        validation_service = get_validation_service()

        params = {
            "INSURER_CODE": "INS001"
            # Missing required additional parameters
        }

        with pytest.raises(ValidationError):
            validation_service.validate_policy_list_request(params)

    def test_validate_policy_detail_request(self):
        """Test PolicyDetailSF validation."""
        validation_service = get_validation_service()

        params = {"MEMBER_CODE": "MEM001"}

        request = validation_service.validate_policy_detail_request(params)
        assert request.MEMBER_CODE == "MEM001"

    def test_validate_claim_list_request_member_code(self):
        """Test ClaimListSF validation with member code."""
        validation_service = get_validation_service()

        params = {"MEMBER_CODE": "MEM001"}

        request = validation_service.validate_claim_list_request(params)
        assert request.MEMBER_CODE == "MEM001"

    def test_validate_claim_list_request_insurer_citizen(self):
        """Test ClaimListSF validation with insurer code and citizen ID."""
        validation_service = get_validation_service()

        params = {"INSURER_CODE": "INS001", "CITIZEN_ID": "1234567890123"}

        request = validation_service.validate_claim_list_request(params)
        assert request.INSURER_CODE == "INS001"
        assert request.CITIZEN_ID == "1234567890123"

    def test_get_policy_list_search_strategy(self):
        """Test policy list search strategy determination."""
        validation_service = get_validation_service()

        # Test citizen ID strategy
        params = {"INSURER_CODE": "INS001", "CITIZEN_ID": "1234567890123"}
        request = validation_service.validate_policy_list_request(params)
        strategy, search_params = validation_service.get_policy_list_search_strategy(
            request
        )

        assert strategy == "citizen_id"
        assert search_params["insurer_code"] == "INS001"
        assert search_params["citizen_id"] == "1234567890123"

    def test_get_claim_list_search_strategy(self):
        """Test claim list search strategy determination."""
        validation_service = get_validation_service()

        # Test member code strategy
        params = {"MEMBER_CODE": "MEM001"}
        request = validation_service.validate_claim_list_request(params)
        strategy, search_params = validation_service.get_claim_list_search_strategy(
            request
        )

        assert strategy == "member_code"
        assert search_params["member_code"] == "MEM001"


class TestPolicyService:
    """Test policy service functionality."""

    def test_policy_service_initialization(self):
        """Test policy service can be initialized."""
        policy_service = get_policy_service()
        assert policy_service is not None
        assert policy_service.policy_manager is not None
        assert policy_service.claim_manager is not None
        assert policy_service.validation_service is not None

    @patch("src.services.policy_service.get_policy_manager")
    @patch("src.services.policy_service.get_claim_manager")
    @patch("src.services.policy_service.get_validation_service")
    def test_get_policy_list_citizen_id(
        self,
        mock_get_validation_service,
        mock_get_claim_manager,
        mock_get_policy_manager,
    ):
        """Test getting policy list by citizen ID."""
        # Mock validation service
        mock_validation = MagicMock()
        mock_request = MagicMock()
        mock_request.INSURER_CODE = "INS001"
        mock_request.CITIZEN_ID = "1234567890123"
        mock_validation.validate_policy_list_request.return_value = mock_request
        mock_validation.get_policy_list_search_strategy.return_value = (
            "citizen_id",
            {"insurer_code": "INS001", "citizen_id": "1234567890123"},
        )
        mock_get_validation_service.return_value = mock_validation

        # Mock policy manager
        mock_policy_manager = MagicMock()
        mock_policy = {
            "MemberCode": "MEM001",
            "CitizenID": "1234567890123",
            "InsurerCode": "INS001",
            "NameTH": "สมชาย",
            "NameEN": "John",
            "MemberStatus": "Active",
            "TitleTH": "นาย",
            "SurnameTH": "ใจดี",
            "TitleEN": "Mr.",
            "SurnameEN": "Jaidee",
            "OtherID": "EMP001",
            "StaffNo": "ST001",
            "InsurerCardNo": "IC001",
            "InsPreviousCardNo": "IPC001",
            "PolicyNo": "POL001",
            "CertificateNo": "CERT001",
            "MemberType": "Principal",
            "PrincipleMemberCode": "MEM001",
            "PrincipleName": "สมชาย ใจดี",
            "VIP": "N",
            "VIPRemarks": "",
            "CardType": "Standard",
            "Language": "TH",
            "InsurerName": "บริษัท ประกันภัย เอ จำกัด",
            "InsurerNameEN": "Insurance Company A Ltd.",
            "CompanyCode": "COM001",
            "CompanyName": "บริษัท ABC จำกัด",
            "CompanyNameEN": "ABC Company Ltd.",
            "BirthDate": "1985-03-15",
            "Gender": "M",
            "Citizenship": "Thai",
            "CountryCode": "TH",
            "PlanCode": "PLAN001",
            "PlanName": "แผนประกันสุขภาพ A",
            "PlanEffFrom": "2024-01-01",
            "PlanEffTo": "2024-12-31",
            "Mobile": "0812345678",
            "Email": "<EMAIL>",
        }
        mock_policy_manager.get_active_policies_by_insurer_code.return_value = [
            mock_policy
        ]
        mock_policy_manager.get_payment_details_by_member_code.return_value = []
        mock_get_policy_manager.return_value = mock_policy_manager

        # Mock claim manager
        mock_claim_manager = MagicMock()
        mock_get_claim_manager.return_value = mock_claim_manager

        # Create a fresh policy service instance
        from src.services.policy_service import PolicyService

        policy_service = PolicyService()

        params = {"INSURER_CODE": "INS001", "CITIZEN_ID": "1234567890123"}

        result = policy_service.get_policy_list(params)
        assert len(result) == 1
        assert result[0].MemberCode == "MEM001"
        assert result[0].CitizenID == "1234567890123"

    @patch("src.services.policy_service.get_policy_manager")
    def test_get_policy_detail_not_found(self, mock_get_policy_manager):
        """Test getting policy detail for non-existent member."""
        # Mock policy manager
        mock_manager = MagicMock()
        mock_manager.get_policy_by_member_code.return_value = None
        mock_get_policy_manager.return_value = mock_manager

        policy_service = get_policy_service()
        params = {"MEMBER_CODE": "NONEXISTENT"}

        with pytest.raises(NotFoundError):
            policy_service.get_policy_detail(params)


class TestClaimService:
    """Test claim service functionality."""

    def test_claim_service_initialization(self):
        """Test claim service can be initialized."""
        claim_service = get_claim_service()
        assert claim_service is not None
        assert claim_service.claim_manager is not None
        assert claim_service.policy_manager is not None
        assert claim_service.validation_service is not None

    @patch("src.services.claim_service.get_claim_manager")
    @patch("src.services.claim_service.get_policy_manager")
    @patch("src.services.claim_service.get_validation_service")
    def test_get_claim_list_member_code(
        self,
        mock_get_validation_service,
        mock_get_policy_manager,
        mock_get_claim_manager,
    ):
        """Test getting claim list by member code."""
        # Mock validation service
        mock_validation = MagicMock()
        mock_request = MagicMock()
        mock_request.MEMBER_CODE = "MEM001"
        mock_validation.validate_claim_list_request.return_value = mock_request
        mock_validation.get_claim_list_search_strategy.return_value = (
            "member_code",
            {"member_code": "MEM001"},
        )
        mock_get_validation_service.return_value = mock_validation

        # Mock claim manager with complete claim data
        mock_claim_manager = MagicMock()
        mock_claim = {
            "ClaimNo": "CLM001",
            "MemberCode": "MEM001",
            "ClaimStatus": "Approved",
            "MainBenefit": "OPD",
            "ClaimSource": "Hospital",
            "ClaimType": "Medical",
            "PatientNameTH": "สมชาย ใจดี",
            "PatientNameEN": "Somchai Jaidee",
            "CitizenID": "1234567890123",
            "VisitDate": "2024-01-15",
            "DischargeDate": "2024-01-15",
            "AccidentDate": "",
            "IncurredAmt": "1500.00",
            "PayableAmt": "1500.00",
            "ContractNo": "CON001",
            "InsurerCode": "INS001",
            "InsurerTH": "บริษัท ประกันภัย เอ จำกัด",
            "InsurerEN": "Insurance Company A Ltd.",
            "CompanyCode": "COM001",
            "CompanyTH": "บริษัท ABC จำกัด",
            "CompanyEN": "ABC Company Ltd.",
            "CardType": "Standard",
            "PolicyNo": "POL001",
            "ProviderCode": "PROV001",
            "ProviderTH": "โรงพยาบาล ABC",
            "ProviderEN": "ABC Hospital",
            "BatchNo": "BATCH001",
            "DiagCode": "Z00.0",
            "DiagTH": "ตรวจสุขภาพทั่วไป",
            "DiagEN": "General health examination",
            "PaymentDate": "2024-01-25",
        }
        mock_claim_manager.get_claims_by_member_code.return_value = [mock_claim]
        mock_get_claim_manager.return_value = mock_claim_manager

        # Mock policy manager
        mock_policy_manager = MagicMock()
        mock_get_policy_manager.return_value = mock_policy_manager

        # Create a fresh claim service instance
        from src.services.claim_service import ClaimService

        claim_service = ClaimService()

        params = {"MEMBER_CODE": "MEM001"}

        result = claim_service.get_claim_list(params)
        assert len(result) == 1
        assert result[0].ClaimNo == "CLM001"
        assert result[0].ClaimStatus == "Approved"
        assert result[0].MemberCode == "MEM001"

    def test_get_valid_claim_statuses(self):
        """Test getting valid claim statuses."""
        claim_service = get_claim_service()
        statuses = claim_service.get_valid_claim_statuses()

        assert isinstance(statuses, list)
        assert "Approved" in statuses
        assert "Authorized" in statuses
        assert "Rejected" in statuses

    def test_validate_claim_status(self):
        """Test claim status validation."""
        claim_service = get_claim_service()

        assert claim_service.validate_claim_status("Approved") is True
        assert claim_service.validate_claim_status("Invalid") is False


class TestPhase4Integration:
    """Integration tests for Phase 4 services."""

    def test_services_integration_with_real_data(self):
        """Test services work with real data."""
        # Initialize data
        initialize_data()

        # Test validation service
        validation_service = get_validation_service()
        params = {"INSURER_CODE": "INS001", "CITIZEN_ID": "1234567890123"}
        request = validation_service.validate_policy_list_request(params)
        assert request.INSURER_CODE == "INS001"

        # Test policy service
        policy_service = get_policy_service()
        # This should not raise an exception even if no data found
        result = policy_service.get_policy_list(params)
        assert isinstance(result, list)

        # Test claim service
        claim_service = get_claim_service()
        claim_params = {"MEMBER_CODE": "MEM001"}
        claim_result = claim_service.get_claim_list(claim_params)
        assert isinstance(claim_result, list)
