"""
Tests for API request and response models.
"""

import pytest
from pydantic import ValidationError as PydanticValidationError
from src.utils.exceptions import ValidationError
from src.models.request_models import (
    PolicyListSFRequest,
    PolicyDetailSFRequest,
    ClaimListSFRequest,
)
from src.models.response_models import (
    PolicyListResponse,
    PolicyDetailSFResponse,
    ClaimHistoryResponse,
    PaymentDetailResponse,
    PolicyDetailResponse,
    BenefitDetailResponse,
    ContractConditionResponse,
    MemberConditionResponse,
)


class TestRequestModels:
    """Test request model validation."""

    def test_policy_list_sf_request_valid_combinations(self):
        """Test valid parameter combinations for PolicyListSF."""
        # Test INSURER_CODE + CITIZEN_ID
        req1 = PolicyListSFRequest(INSURER_CODE="INS001", CITIZEN_ID="*************")
        assert req1.INSURER_CODE == "INS001"
        assert req1.CITIZEN_ID == "*************"

        # Test INSURER_CODE + POLICY_NO + NAME_TH
        req2 = PolicyListSFRequest(
            INSURER_CODE="INS001", POLICY_NO="POL001", NAME_TH="สมชาย"
        )
        assert req2.INSURER_CODE == "INS001"
        assert req2.POLICY_NO == "POL001"
        assert req2.NAME_TH == "สมชาย"

        # Test INSURER_CODE + NAME_EN
        req3 = PolicyListSFRequest(INSURER_CODE="INS001", NAME_EN="John")
        assert req3.INSURER_CODE == "INS001"
        assert req3.NAME_EN == "John"

    def test_policy_list_sf_request_invalid_combinations(self):
        """Test invalid parameter combinations for PolicyListSF."""
        # Test missing INSURER_CODE
        with pytest.raises(PydanticValidationError):
            PolicyListSFRequest(CITIZEN_ID="*************")

        # Test INSURER_CODE only (no additional parameters)
        with pytest.raises(ValidationError):
            PolicyListSFRequest(INSURER_CODE="INS001")

        # Test invalid combination (POLICY_NO without NAME)
        with pytest.raises(ValidationError):
            PolicyListSFRequest(INSURER_CODE="INS001", POLICY_NO="POL001")

        # Test conflicting parameters (both NAME_TH and NAME_EN)
        with pytest.raises(ValidationError):
            PolicyListSFRequest(INSURER_CODE="INS001", NAME_TH="สมชาย", NAME_EN="John")

    def test_policy_detail_sf_request(self):
        """Test PolicyDetailSF request validation."""
        # Valid request
        req = PolicyDetailSFRequest(MEMBER_CODE="MEM001")
        assert req.MEMBER_CODE == "MEM001"

        # Missing required field
        with pytest.raises(PydanticValidationError):
            PolicyDetailSFRequest()

    def test_claim_list_sf_request_valid_combinations(self):
        """Test valid parameter combinations for ClaimListSF."""
        # Test MEMBER_CODE only
        req1 = ClaimListSFRequest(MEMBER_CODE="MEM001")
        assert req1.MEMBER_CODE == "MEM001"
        assert req1.INSURER_CODE is None
        assert req1.CITIZEN_ID is None

        # Test INSURER_CODE + CITIZEN_ID
        req2 = ClaimListSFRequest(INSURER_CODE="INS001", CITIZEN_ID="*************")
        assert req2.INSURER_CODE == "INS001"
        assert req2.CITIZEN_ID == "*************"
        assert req2.MEMBER_CODE is None

    def test_claim_list_sf_request_invalid_combinations(self):
        """Test invalid parameter combinations for ClaimListSF."""
        # Test no parameters
        with pytest.raises(ValidationError):
            ClaimListSFRequest()

        # Test INSURER_CODE only (missing CITIZEN_ID)
        with pytest.raises(ValidationError):
            ClaimListSFRequest(INSURER_CODE="INS001")

        # Test CITIZEN_ID only (missing INSURER_CODE)
        with pytest.raises(ValidationError):
            ClaimListSFRequest(CITIZEN_ID="*************")

        # Test conflicting parameters (MEMBER_CODE with others)
        with pytest.raises(ValidationError):
            ClaimListSFRequest(MEMBER_CODE="MEM001", INSURER_CODE="INS001")


class TestResponseModels:
    """Test response model creation."""

    def test_payment_detail_response(self):
        """Test PaymentDetailResponse model."""
        payment = PaymentDetailResponse(
            PaymentMethod="Bank Transfer",
            PayeeName="John Doe",
            BankAccNo="**********",
            BankName="Test Bank",
            Primary="Y",
        )
        assert payment.PaymentMethod == "Bank Transfer"
        assert payment.PayeeName == "John Doe"

    def test_policy_list_response(self):
        """Test PolicyListResponse model."""
        payment_details = [
            PaymentDetailResponse(
                PaymentMethod="Bank Transfer",
                PayeeName="John Doe",
                BankAccNo="**********",
                BankName="Test Bank",
                Primary="Y",
            )
        ]

        policy = PolicyListResponse(
            MemberCode="MEM001",
            MemberStatus="Active",
            TitleTH="นาย",
            NameTH="สมชาย",
            SurnameTH="ใจดี",
            TitleEN="Mr.",
            NameEN="Somchai",
            SurnameEN="Jaidee",
            CitizenID="*************",
            OtherID="EMP001",
            StaffNo="ST001",
            InsurerCardNo="IC001",
            InsPreviousCardNo="IPC001",
            PolicyNo="POL001",
            CertificateNo="CERT001",
            MemberType="Principal",
            PrincipleMemberCode="MEM001",
            PrincipleName="สมชาย ใจดี",
            VIP="N",
            VIPRemarks="",
            CardType="Standard",
            Language="TH",
            InsurerCode="INS001",
            InsurerName="บริษัท ประกันภัย เอ จำกัด",
            InsurerNameEN="Insurance Company A Ltd.",
            CompanyCode="COM001",
            CompanyName="บริษัท ABC จำกัด",
            CompanyNameEN="ABC Company Ltd.",
            BirthDate="1985-03-15",
            Gender="M",
            Citizenship="Thai",
            CountryCode="TH",
            PlanCode="PLAN001",
            PlanName="แผนประกันสุขภาพพื้นฐาน",
            PlanEffFrom="2024-01-01",
            PlanEffTo="2024-12-31",
            Mobile="**********",
            Email="<EMAIL>",
            PaymentDetail=payment_details,
        )
        assert policy.MemberCode == "MEM001"
        assert len(policy.PaymentDetail) == 1

    def test_claim_history_response(self):
        """Test ClaimHistoryResponse model."""
        claim = ClaimHistoryResponse(
            ClaimNo="CLM001",
            MainBenefit="ประกันสุขภาพ",
            ClaimSource="Hospital",
            ClaimType="IPD",
            PatientNameTH="สมชาย ใจดี",
            PatientNameEN="Somchai Jaidee",
            ClaimStatus="Approved",
            CitizenID="*************",
            VisitDate="2024-01-15",
            DischargeDate="2024-01-17",
            AccidentDate="",
            IncurredAmt="50000",
            PayableAmt="45000",
            ContractNo="CON001",
            InsurerCode="INS001",
            InsurerTH="บริษัท ประกันภัย เอ จำกัด",
            InsurerEN="Insurance Company A Ltd.",
            CompanyCode="COM001",
            CompanyTH="บริษัท ABC จำกัด",
            CompanyEN="ABC Company Ltd.",
            CardType="Standard",
            PolicyNo="POL001",
            ProviderCode="PROV001",
            ProviderTH="โรงพยาบาลเอ",
            ProviderEN="Hospital A",
            BatchNo="BATCH001",
            DiagCode="DIAG001",
            DiagTH="ไข้หวัดใหญ่",
            DiagEN="Influenza",
            PaymentDate="2024-01-20",
            MemberCode="MEM001",
        )
        assert claim.ClaimNo == "CLM001"
        assert claim.ClaimStatus == "Approved"

    def test_policy_detail_sf_response(self):
        """Test complete PolicyDetailSF response model."""
        policy_details = [
            PolicyDetailResponse(
                MainBenefitCode="MB001",
                MainBenefit="ประกันสุขภาพ",
                MainBenefitEN="Health Insurance",
                MainPlanLimitDesc="ค่ารักษาพยาบาล",
                MainPlanAmount="1000000",
                MainPlanUnit1="บาท",
                MainPlanUnit2="ต่อปี",
                MainPlanBalance="800000",
            )
        ]

        benefit_list = [
            BenefitDetailResponse(
                BenefitCode="BEN001",
                BenefitTH="ค่ารักษาพยาบาล",
                BenefitEN="Medical Treatment",
                SubBenefitCode="SUB001",
                SubBenefitTH="ค่าห้องพัก",
                SubBenefitEN="Room Charge",
                LimitCode="LIM001",
                LimitAmt="5000",
                LimitUnit="บาท/วัน",
                BalanceCode="BAL001",
                BalanceLimitAmt="4000",
                BalanceUnit="บาท/วัน",
                ComSubLimitCode="CSL001",
                ComSubLimitAmt="100000",
                ComSubLimitUnit="บาท/ปี",
                BalComSubLimitCode="BCSL001",
                BalComSubLimitAmt="80000",
                BalComSubLimitUnit="บาท/ปี",
                ComLimitCode="CL001",
                ComLimitAmt="500000",
                ComLimitUnit="บาท/ปี",
                BalComLimitCode="BCL001",
                BalComLimitAmt="400000",
                BalComLimitUnit="บาท/ปี",
            )
        ]

        response = PolicyDetailSFResponse(
            ListPolicyDetail=policy_details,
            BenefitList=benefit_list,
            ListContractCondition=[],
            ListMemberCondition=[],
            ListClaimHistory=[],
        )
        assert len(response.ListPolicyDetail) == 1
        assert len(response.BenefitList) == 1
