"""
Unit tests for mock data loading functionality.
"""

import pytest
import tempfile
import csv
from pathlib import Path
from unittest.mock import patch, MagicMock

from src.data.mock_data import MockData<PERSON>oa<PERSON>, get_data_loader, initialize_data
from src.utils.exceptions import DataLoadingError


class TestMockDataLoader:
    """Test cases for MockDataLoader class."""

    def test_init(self):
        """Test MockDataLoader initialization."""
        loader = MockDataLoader()

        assert loader.settings is not None
        assert loader.csv_path is not None
        assert loader.policies == []
        assert loader.payment_details == []
        assert loader.policy_details == []
        assert loader.benefits == []
        assert loader.contract_conditions == []
        assert loader.member_conditions == []
        assert loader.claims == []
        assert loader._loaded is False

    def test_load_all_data_already_loaded(self):
        """Test that load_all_data skips reload when already loaded."""
        loader = MockDataLoader()
        loader._loaded = True

        with patch.object(loader, "_load_csv_file") as mock_load:
            loader.load_all_data()
            mock_load.assert_not_called()

    def test_load_csv_file_success(self):
        """Test successful CSV file loading."""
        # Create temporary CSV file
        with tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=False) as f:
            writer = csv.writer(f)
            writer.writerow(["MemberCode", "MemberStatus", "NameTH"])
            writer.writerow(["MEM001", "Active", "สมชาย"])
            writer.writerow(["MEM002", "Active", "สุดา"])
            temp_path = f.name

        try:
            loader = MockDataLoader()

            # Mock the model class
            class MockModel:
                def __init__(self, **kwargs):
                    self.data = kwargs

                def dict(self):
                    return self.data

            # Mock the csv_path to point to our temp file
            loader.csv_path = Path(temp_path).parent

            records = loader._load_csv_file(Path(temp_path).name, MockModel)

            assert len(records) == 2
            assert records[0].data["MemberCode"] == "MEM001"
            assert records[1].data["MemberCode"] == "MEM002"

        finally:
            Path(temp_path).unlink()

    def test_load_csv_file_not_found(self):
        """Test CSV file loading when file doesn't exist."""
        loader = MockDataLoader()

        with pytest.raises(DataLoadingError, match="CSV file not found"):
            loader._load_csv_file("nonexistent.csv", dict)

    def test_load_csv_file_parse_error(self):
        """Test CSV file loading with parse errors."""
        # Create temporary CSV file with invalid data
        with tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=False) as f:
            writer = csv.writer(f)
            writer.writerow(["MemberCode", "MemberStatus"])
            writer.writerow(["MEM001", "Active"])
            writer.writerow(["MEM002"])  # Missing column
            temp_path = f.name

        try:
            loader = MockDataLoader()

            # Mock model that raises exception for invalid data
            class MockModel:
                def __init__(self, **kwargs):
                    if (
                        kwargs.get("MemberStatus") is None
                        or kwargs.get("MemberStatus") == ""
                    ):
                        raise ValueError("Missing required fields")
                    self.data = kwargs

            loader.csv_path = Path(temp_path).parent

            # Should continue processing despite parse error
            records = loader._load_csv_file(Path(temp_path).name, MockModel)

            # Should only have 1 valid record
            assert len(records) == 1
            assert records[0].data["MemberCode"] == "MEM001"

        finally:
            Path(temp_path).unlink()

    def test_get_methods_ensure_loaded(self):
        """Test that get methods ensure data is loaded."""
        loader = MockDataLoader()

        with patch.object(loader, "load_all_data") as mock_load:
            loader.get_policies()
            mock_load.assert_called_once()

            mock_load.reset_mock()
            loader.get_payment_details()
            mock_load.assert_called_once()

    def test_get_data_loader_singleton(self):
        """Test that get_data_loader returns singleton instance."""
        loader1 = get_data_loader()
        loader2 = get_data_loader()

        assert loader1 is loader2

    @patch("src.data.mock_data.get_data_loader")
    def test_initialize_data(self, mock_get_loader):
        """Test initialize_data function."""
        mock_loader = MagicMock()
        mock_get_loader.return_value = mock_loader

        initialize_data()

        mock_loader.load_all_data.assert_called_once()


class TestMockDataLoaderIntegration:
    """Integration tests for MockDataLoader with real CSV files."""

    @patch("src.data.mock_data.get_settings")
    def test_load_real_csv_files(self, mock_get_settings):
        """Test loading real CSV files from the project."""
        # Mock settings to point to real CSV directory
        mock_settings = MagicMock()
        mock_settings.csv_data_path = "data/csv"
        mock_get_settings.return_value = mock_settings

        loader = MockDataLoader()

        try:
            loader.load_all_data()

            # Verify data was loaded
            assert len(loader.get_policies()) > 0
            assert len(loader.get_payment_details()) > 0
            assert len(loader.get_policy_details()) > 0
            assert len(loader.get_benefits()) > 0
            assert len(loader.get_contract_conditions()) > 0
            assert len(loader.get_member_conditions()) > 0
            assert len(loader.get_claims()) > 0

            # Verify data structure
            policies = loader.get_policies()
            assert all(hasattr(policy, "MemberCode") for policy in policies)
            assert all(hasattr(policy, "InsurerCode") for policy in policies)

        except Exception as e:
            pytest.skip(f"Real CSV files not available: {e}")
