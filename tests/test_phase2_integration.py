"""
Integration tests for Phase 2 data layer components.
Tests the complete data loading and management functionality with real CSV files.
"""

import pytest
from src.data.mock_data import get_data_loader, initialize_data
from src.data.policy_data import get_policy_manager
from src.data.claim_data import get_claim_manager


class TestPhase2Integration:
    """Integration tests for Phase 2 data layer."""

    def test_complete_data_loading_flow(self):
        """Test the complete data loading flow with real CSV files."""
        # Initialize data
        initialize_data()

        # Get data loader and verify data is loaded
        loader = get_data_loader()

        policies = loader.get_policies()
        payment_details = loader.get_payment_details()
        policy_details = loader.get_policy_details()
        benefits = loader.get_benefits()
        contract_conditions = loader.get_contract_conditions()
        member_conditions = loader.get_member_conditions()
        claims = loader.get_claims()

        # Verify data was loaded
        assert len(policies) > 0, "Policies should be loaded"
        assert len(payment_details) > 0, "Payment details should be loaded"
        assert len(policy_details) > 0, "Policy details should be loaded"
        assert len(benefits) > 0, "Benefits should be loaded"
        assert len(contract_conditions) > 0, "Contract conditions should be loaded"
        assert len(member_conditions) > 0, "Member conditions should be loaded"
        assert len(claims) > 0, "Claims should be loaded"

        # Verify data structure
        assert hasattr(policies[0], "MemberCode"), "Policy should have MemberCode"
        assert hasattr(policies[0], "InsurerCode"), "Policy should have InsurerCode"
        assert hasattr(claims[0], "ClaimNo"), "Claim should have ClaimNo"
        assert hasattr(claims[0], "MemberCode"), "Claim should have MemberCode"

    def test_policy_manager_integration(self):
        """Test policy manager with real data."""
        # Initialize data
        initialize_data()

        # Get policy manager
        policy_manager = get_policy_manager()

        # Test getting policies by different criteria
        policies = policy_manager.get_active_policies_by_insurer_code("INS001")
        assert isinstance(policies, list), "Should return a list"

        # If there are policies, test individual policy lookup
        if policies:
            first_policy = policies[0]
            member_code = first_policy.get("MemberCode")

            # Test getting policy by member code
            policy = policy_manager.get_policy_by_member_code(member_code)
            assert policy is not None, "Should find policy by member code"
            assert policy["MemberCode"] == member_code, "Should return correct policy"

            # Test getting payment details
            payment_details = policy_manager.get_payment_details_by_member_code(
                member_code
            )
            assert isinstance(payment_details, list), (
                "Should return payment details list"
            )

            # Test getting policy details
            policy_detail = policy_manager.get_policy_detail_by_member_code(member_code)
            # Policy detail might be None if not available for this member
            if policy_detail:
                assert policy_detail["MemberCode"] == member_code, (
                    "Should return correct policy detail"
                )

            # Test getting benefits
            benefits = policy_manager.get_benefits_by_member_code(member_code)
            assert isinstance(benefits, list), "Should return benefits list"

    def test_claim_manager_integration(self):
        """Test claim manager with real data."""
        # Initialize data
        initialize_data()

        # Get claim manager
        claim_manager = get_claim_manager()

        # Test getting valid claim statuses
        valid_statuses = claim_manager.get_valid_claim_statuses()
        assert len(valid_statuses) == 7, "Should have 7 valid claim statuses"
        assert "Approved" in valid_statuses, "Should include Approved status"
        assert "Authorized" in valid_statuses, "Should include Authorized status"

        # Get all claims and test if any exist
        loader = get_data_loader()
        all_claims = loader.get_claims()

        if all_claims:
            # Find a claim with valid status
            valid_claim = None
            for claim in all_claims:
                claim_dict = claim.model_dump()
                if claim_manager.validate_claim_status(
                    claim_dict.get("ClaimStatus", "")
                ):
                    valid_claim = claim_dict
                    break

            if valid_claim:
                member_code = valid_claim.get("MemberCode")
                citizen_id = valid_claim.get("CitizenID")

                # Test getting claims by member code
                claims = claim_manager.get_claims_by_member_code(member_code)
                assert isinstance(claims, list), "Should return claims list"

                # Test getting claims by citizen ID
                claims_by_citizen = claim_manager.get_claims_by_citizen_id(citizen_id)
                assert isinstance(claims_by_citizen, list), "Should return claims list"

                # Test getting conditions
                all_conditions = claim_manager.get_all_conditions_by_member_code(
                    member_code
                )
                assert "contract_conditions" in all_conditions, (
                    "Should have contract conditions key"
                )
                assert "member_conditions" in all_conditions, (
                    "Should have member conditions key"
                )
                assert isinstance(all_conditions["contract_conditions"], list), (
                    "Contract conditions should be list"
                )
                assert isinstance(all_conditions["member_conditions"], list), (
                    "Member conditions should be list"
                )

    def test_data_consistency(self):
        """Test data consistency across different managers."""
        # Initialize data
        initialize_data()

        # Get managers
        policy_manager = get_policy_manager()
        claim_manager = get_claim_manager()

        # Get a policy with active status
        active_policies = policy_manager.get_active_policies_by_insurer_code("INS001")

        if active_policies:
            policy = active_policies[0]
            member_code = policy.get("MemberCode")
            citizen_id = policy.get("CitizenID")

            # Verify the same member exists in claims (if they have claims)
            claims = claim_manager.get_claims_by_member_code(member_code)

            # If claims exist, verify citizen ID consistency
            for claim in claims:
                assert claim.get("CitizenID") == citizen_id, (
                    "Citizen ID should be consistent"
                )

    def test_filtering_functionality(self):
        """Test advanced filtering functionality."""
        # Initialize data
        initialize_data()

        # Get policy manager
        policy_manager = get_policy_manager()

        # Test filtering with multiple criteria
        policies = policy_manager.filter_policies_by_criteria(insurer_code="INS001")
        assert isinstance(policies, list), "Should return policies list"

        # All returned policies should have the correct insurer code
        for policy in policies:
            assert policy.get("InsurerCode") == "INS001", (
                "All policies should have correct insurer code"
            )

        # Test filtering with company code
        if policies:
            first_policy = policies[0]
            company_code = first_policy.get("CompanyCode")

            filtered_policies = policy_manager.filter_policies_by_criteria(
                insurer_code="INS001", company_code=company_code
            )

            # All returned policies should have both criteria
            for policy in filtered_policies:
                assert policy.get("InsurerCode") == "INS001", (
                    "Should have correct insurer code"
                )
                assert policy.get("CompanyCode") == company_code, (
                    "Should have correct company code"
                )

    def test_error_handling(self):
        """Test error handling for non-existent data."""
        # Initialize data
        initialize_data()

        # Get managers
        policy_manager = get_policy_manager()
        claim_manager = get_claim_manager()

        # Test getting non-existent policy
        policy = policy_manager.get_policy_by_member_code("NONEXISTENT")
        assert policy is None, "Should return None for non-existent member"

        # Test getting non-existent claims
        claims = claim_manager.get_claims_by_member_code("NONEXISTENT")
        assert claims == [], "Should return empty list for non-existent member"

        # Test getting policies with non-existent insurer
        policies = policy_manager.get_active_policies_by_insurer_code("NONEXISTENT")
        assert policies == [], "Should return empty list for non-existent insurer"
